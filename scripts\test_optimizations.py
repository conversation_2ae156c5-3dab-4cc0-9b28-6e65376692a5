#!/usr/bin/env python3
"""
Comprehensive Testing Script for Backup Optimizations

This script tests all the performance optimization features to ensure they work correctly:
- Progressive chunk sizing
- Adaptive compression
- Smart throttling
- Intelligent retry logic
- Performance tracking

Run this script to validate that all optimization features are working properly.
"""

import os
import sys
import time
import logging
import tempfile
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from tngd_backup.core.performance_optimizer import PerformanceOptimizer
from tngd_backup.utils.performance_tracker import PerformanceTracker
from tngd_backup.utils.monitoring import ResourceMonitor
from tngd_backup.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, smart_retry
from tngd_backup.core.compression_service import CompressionService
from tngd_backup.core.streaming_processor import ChunkManager, StreamingConfig


class TestPerformanceOptimizations(unittest.TestCase):
    """Test suite for performance optimization features."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = Path(tempfile.mkdtemp())
        self.logger = logging.getLogger(__name__)
        
        # Create test configuration
        self.test_config = {
            "performance_tracking": {"enabled": True},
            "progressive_chunk_sizing": {"enabled": True},
            "adaptive_compression": {"enabled": True},
            "smart_throttling": {"enabled": True},
            "smart_retry": {"enabled": True}
        }
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_performance_tracker(self):
        """Test performance tracking functionality."""
        print("\n🧪 Testing Performance Tracker...")
        
        tracker = PerformanceTracker(max_history=100)
        
        # Record some test operations
        tracker.record_operation("test_operation_1", 2.5, 1000, True, {"test": "data"})
        tracker.record_operation("test_operation_1", 3.0, 800, True)
        tracker.record_operation("test_operation_2", 1.5, 1500, True)
        tracker.record_operation("test_operation_1", 5.0, 500, False, {"error": "timeout"})
        
        # Test analysis
        analysis = tracker.analyze_performance()
        self.assertIn("test_operation_1", analysis)
        self.assertIn("test_operation_2", analysis)
        
        op1_analysis = analysis["test_operation_1"]
        self.assertEqual(op1_analysis.total_operations, 3)
        self.assertAlmostEqual(op1_analysis.success_rate, 2/3, places=2)
        
        # Test recommendations
        recommendations = tracker.get_system_recommendations()
        self.assertIsInstance(recommendations, list)
        
        print("✅ Performance Tracker tests passed")
    
    def test_chunk_manager_progressive_sizing(self):
        """Test progressive chunk sizing functionality."""
        print("\n🧪 Testing Progressive Chunk Sizing...")
        
        config = StreamingConfig()
        config.enable_progressive_sizing = True
        config.default_chunk_size = 10000
        config.min_chunk_size = 1000
        config.max_chunk_size = 50000
        
        chunk_manager = ChunkManager(config)
        
        # Test initial chunk calculation
        chunks = chunk_manager.calculate_chunks(100000, "test_table")
        self.assertGreater(len(chunks), 0)
        
        # Test performance recording
        chunk_manager.record_chunk_performance(1, 2.0, 50.0, True)
        chunk_manager.record_chunk_performance(2, 1.5, 45.0, True)
        chunk_manager.record_chunk_performance(3, 3.0, 80.0, True)
        
        # Test progressive sizing with good performance
        new_size = chunk_manager._get_progressive_chunk_size(100000, "test_table")
        self.assertIsInstance(new_size, int)
        self.assertGreaterEqual(new_size, config.min_chunk_size)
        self.assertLessEqual(new_size, config.max_chunk_size)
        
        print("✅ Progressive Chunk Sizing tests passed")
    
    def test_adaptive_compression(self):
        """Test adaptive compression functionality."""
        print("\n🧪 Testing Adaptive Compression...")
        
        compression_service = CompressionService()
        
        # Test data characteristics analysis
        test_file = self.test_dir / "test_data.txt"
        test_file.write_text("This is test data for compression analysis.")
        
        characteristics = compression_service._analyze_data_characteristics(str(test_file))
        self.assertIn('size_mb', characteristics)
        self.assertIn('compressibility', characteristics)
        
        # Test compression level selection
        level = compression_service._select_optimal_compression_level(characteristics)
        self.assertIsInstance(level, int)
        self.assertGreaterEqual(level, 1)
        self.assertLessEqual(level, 6)
        
        # Test adaptive compression
        success, output_file, stats = compression_service.adaptive_compress(
            str(test_file), str(self.test_dir / "test_compressed.tar.gz")
        )
        self.assertTrue(success)
        self.assertTrue(Path(output_file).exists())
        self.assertIn('compression_ratio', stats)
        
        print("✅ Adaptive Compression tests passed")
    
    def test_smart_throttling(self):
        """Test smart throttling functionality."""
        print("\n🧪 Testing Smart Throttling...")
        
        try:
            monitor = ResourceMonitor()
            
            # Test throttling status
            status = monitor.get_throttling_status()
            self.assertIn('throttling_active', status)
            self.assertIn('adaptive_thresholds', status)
            
            # Test throttling application (with mock high resource usage)
            with patch.object(monitor, 'get_system_health') as mock_health:
                mock_health.return_value = Mock(
                    memory_percent=95.0,
                    cpu_percent=90.0,
                    thread_count=100
                )
                
                monitor.apply_throttling("test_operation")
                
                # Check that throttling was applied
                status_after = monitor.get_throttling_status()
                self.assertTrue(status_after['throttling_active'])
            
            print("✅ Smart Throttling tests passed")
            
        except ImportError:
            print("⚠️ Smart Throttling tests skipped (psutil not available)")
    
    def test_smart_retry_logic(self):
        """Test smart retry logic functionality."""
        print("\n🧪 Testing Smart Retry Logic...")
        
        error_handler = ErrorHandler()
        
        # Test error classification
        network_error = ConnectionError("Connection timeout")
        memory_error = MemoryError("Out of memory")
        validation_error = ValueError("Invalid input")
        
        self.assertEqual(error_handler.classify_error(network_error), 'network')
        self.assertEqual(error_handler.classify_error(memory_error), 'memory')
        self.assertEqual(error_handler.classify_error(validation_error), 'validation')
        
        # Test retry strategy selection
        network_strategy = error_handler.get_retry_strategy(network_error, "test_op")
        self.assertIn('max_retries', network_strategy)
        self.assertIn('base_delay_seconds', network_strategy)
        
        # Test retry decision
        should_retry, delay = error_handler.should_retry(network_error, 1, "test_operation")
        self.assertTrue(should_retry)
        self.assertGreater(delay, 0)
        
        # Test max retries exceeded
        should_retry_max, _ = error_handler.should_retry(network_error, 10, "test_operation")
        self.assertFalse(should_retry_max)
        
        # Test smart retry decorator
        @smart_retry("test_function")
        def failing_function(fail_count=3):
            if hasattr(failing_function, 'call_count'):
                failing_function.call_count += 1
            else:
                failing_function.call_count = 1
            
            if failing_function.call_count <= fail_count:
                raise ConnectionError("Simulated network error")
            return "success"
        
        # This should succeed after retries
        result = failing_function(fail_count=2)
        self.assertEqual(result, "success")
        self.assertGreaterEqual(failing_function.call_count, 2)
        
        print("✅ Smart Retry Logic tests passed")
    
    def test_performance_optimizer_integration(self):
        """Test the integrated performance optimizer."""
        print("\n🧪 Testing Performance Optimizer Integration...")
        
        # Create test config file
        config_file = self.test_dir / "test_config.json"
        import json
        with open(config_file, 'w') as f:
            json.dump(self.test_config, f)
        
        optimizer = PerformanceOptimizer(str(config_file))
        
        # Test optimization summary
        summary = optimizer.get_optimization_summary()
        self.assertIn('performance_tracking', summary)
        self.assertIn('progressive_chunk_sizing', summary)
        
        # Test chunk size optimization
        performance_data = {
            'avg_duration': 30.0,
            'success_rate': 0.95,
            'memory_pressure': 60.0
        }
        
        optimized_size = optimizer.optimize_chunk_size("test_operation", 10000, performance_data)
        self.assertIsInstance(optimized_size, int)
        self.assertGreater(optimized_size, 0)
        
        # Test compression optimization
        data_characteristics = {
            'size_mb': 50,
            'compressibility': 'high',
            'data_type': 'text'
        }
        
        compression_settings = optimizer.optimize_compression(data_characteristics)
        self.assertIn('algorithm', compression_settings)
        self.assertIn('level', compression_settings)
        
        # Test performance recording
        optimizer.record_operation_performance("test_op", 2.5, 1000, True)
        
        # Test performance analysis
        analysis = optimizer.get_performance_analysis()
        self.assertIn('timestamp', analysis)
        
        print("✅ Performance Optimizer Integration tests passed")
    
    def test_optimization_recommendations(self):
        """Test optimization recommendation generation."""
        print("\n🧪 Testing Optimization Recommendations...")
        
        tracker = PerformanceTracker()
        
        # Record various performance scenarios
        # Good performance
        for i in range(5):
            tracker.record_operation("good_operation", 1.0, 5000, True)
        
        # Poor performance
        for i in range(5):
            tracker.record_operation("slow_operation", 120.0, 100, True)
        
        # High memory usage (simulated)
        for i in range(3):
            tracker.record_operation("memory_intensive", 30.0, 1000, True, 
                                   {'memory_usage_mb': 3000})
        
        # Failed operations
        for i in range(3):
            tracker.record_operation("failing_operation", 10.0, 0, False)
        
        # Get recommendations
        recommendations = tracker.get_system_recommendations()
        self.assertIsInstance(recommendations, list)
        self.assertGreater(len(recommendations), 0)
        
        # Analyze specific operations
        analysis = tracker.analyze_performance()
        
        if "slow_operation" in analysis:
            slow_analysis = analysis["slow_operation"]
            self.assertIn("Consider parallel processing", 
                         " ".join(slow_analysis.recommendations))
        
        print("✅ Optimization Recommendations tests passed")


def run_integration_test():
    """Run a comprehensive integration test."""
    print("\n🚀 Running Integration Test...")
    
    try:
        # Test the complete optimization workflow
        optimizer = PerformanceOptimizer()
        
        # Simulate a backup operation
        operation_context = {
            'operation': 'backup_table_test',
            'table_name': 'test_table',
            'chunk_size': 50000,
            'performance_data': {
                'avg_duration': 45.0,
                'success_rate': 0.92,
                'memory_pressure': 75.0
            },
            'data_characteristics': {
                'size_mb': 200,
                'compressibility': 'high',
                'data_type': 'text'
            }
        }
        
        # Apply optimizations
        optimizations = optimizer.apply_optimizations(operation_context)
        print(f"Applied optimizations: {list(optimizations.keys())}")
        
        # Record performance
        optimizer.record_operation_performance(
            "backup_table_test", 42.0, 2500, True, 
            {'optimizations': optimizations}
        )
        
        # Get analysis
        analysis = optimizer.get_performance_analysis()
        print(f"Performance analysis completed: {len(analysis.get('operation_analyses', {}))} operations analyzed")
        
        print("✅ Integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def main():
    """Main test runner."""
    print("🧪 TNGD Backup System - Optimization Testing Suite")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(
        level=logging.WARNING,  # Reduce noise during testing
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run unit tests
    print("\n📋 Running Unit Tests...")
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestPerformanceOptimizations)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=1, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    # Print results
    if result.wasSuccessful():
        print(f"✅ All {result.testsRun} unit tests passed!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        for test, traceback in result.failures + result.errors:
            print(f"   Failed: {test}")
    
    # Run integration test
    integration_success = run_integration_test()
    
    # Final summary
    print("\n" + "=" * 60)
    if result.wasSuccessful() and integration_success:
        print("🎉 All optimization tests passed! Your system is ready for optimized backups.")
        print("\nNext steps:")
        print("1. Run: python scripts/enable_optimizations.py")
        print("2. Start optimized backup: python scripts/optimization/backup_with_optimizations.py")
        return 0
    else:
        print("⚠️ Some tests failed. Please review the output above and fix any issues.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
