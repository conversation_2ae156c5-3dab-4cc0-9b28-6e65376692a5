#!/usr/bin/env python3
"""
Resource Monitoring and System Health Utilities

This module provides comprehensive monitoring capabilities for the TNGD backup system:
- Real-time resource monitoring
- System health checks
- Performance metrics collection
- Alert and notification system
"""

import os
import time
import psutil
import threading
import logging
from datetime import datetime
from typing import Dict, Any, List
from dataclasses import dataclass

from ..constants import BackupConstants


@dataclass
class SystemHealth:
    """System health metrics snapshot."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_percent: float
    thread_count: int
    process_count: int
    network_connections: int
    status: str  # healthy, warning, critical


class ResourceMonitor:
    """
    Real-time system resource monitor with alerting capabilities.
    
    Monitors CPU, memory, disk, and thread usage with configurable thresholds
    and automatic alerting for resource exhaustion conditions.
    """
    
    def __init__(self, log_file: str = "data/logs/resource_monitor.log"):
        """Initialize resource monitor."""
        self.log_file = log_file
        self.setup_logging()
        self.logger = logging.getLogger(f"{__name__}.ResourceMonitor")
        
        # Thresholds
        self.cpu_warning = BackupConstants.CPU_WARNING_THRESHOLD
        self.cpu_critical = BackupConstants.CPU_CRITICAL_THRESHOLD
        self.memory_warning = BackupConstants.MEMORY_WARNING_THRESHOLD
        self.memory_critical = BackupConstants.MEMORY_CRITICAL_THRESHOLD
        self.thread_warning = BackupConstants.THREAD_WARNING_THRESHOLD
        self.thread_critical = BackupConstants.THREAD_CRITICAL_THRESHOLD
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread = None
        self.health_history: List[SystemHealth] = []
        self.max_history = 1000

        # Smart throttling state
        self.throttling_active = False
        self.throttling_history = []
        self.last_throttle_time = 0
        self.throttle_cooldown = 30  # seconds
        self.adaptive_thresholds = {
            'memory_warning': self.memory_warning,
            'memory_critical': self.memory_critical,
            'cpu_warning': self.cpu_warning,
            'cpu_critical': self.cpu_critical
        }
        
        self.logger.info("ResourceMonitor initialized")
    
    def setup_logging(self):
        """Setup logging for resource monitor."""
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

        # Create logger for this module
        logger = logging.getLogger(f"{__name__}.ResourceMonitor")
        logger.setLevel(logging.INFO)

        # Remove any existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Create file handler
        handler = logging.FileHandler(self.log_file)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)

        # Prevent propagation to root logger to avoid conflicts
        logger.propagate = False
    
    def get_system_health(self) -> SystemHealth:
        """Get current system health metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_mb = memory.used / (1024 * 1024)
            
            # Disk usage
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            
            # Process and thread counts
            process_count = len(psutil.pids())
            thread_count = sum(p.num_threads() for p in psutil.process_iter(['num_threads']) 
                             if p.info['num_threads'] is not None)
            
            # Network connections
            network_connections = len(psutil.net_connections())
            
            # Determine status
            status = self._determine_status(cpu_percent, memory_percent, thread_count)
            
            return SystemHealth(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_mb=memory_mb,
                disk_percent=disk_percent,
                thread_count=thread_count,
                process_count=process_count,
                network_connections=network_connections,
                status=status
            )
        
        except Exception as e:
            self.logger.error(f"Failed to get system health: {e}")
            return SystemHealth(
                timestamp=datetime.now(),
                cpu_percent=0, memory_percent=0, memory_mb=0,
                disk_percent=0, thread_count=0, process_count=0,
                network_connections=0, status="error"
            )
    
    def _determine_status(self, cpu: float, memory: float, threads: int) -> str:
        """Determine system status based on metrics."""
        if (cpu >= self.cpu_critical or 
            memory >= self.memory_critical or 
            threads >= self.thread_critical):
            return "critical"
        elif (cpu >= self.cpu_warning or 
              memory >= self.memory_warning or 
              threads >= self.thread_warning):
            return "warning"
        else:
            return "healthy"
    
    def start_monitoring(self, interval: int = 30):
        """Start continuous monitoring."""
        if self.monitoring:
            self.logger.warning("Monitoring already started")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            name="resource-monitor",
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info(f"Started resource monitoring (interval: {interval}s)")
    
    def stop_monitoring(self):
        """Stop monitoring."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Stopped resource monitoring")
    
    def _monitor_loop(self, interval: int):
        """Main monitoring loop."""
        while self.monitoring:
            try:
                health = self.get_system_health()
                self.health_history.append(health)
                
                # Trim history
                if len(self.health_history) > self.max_history:
                    self.health_history = self.health_history[-self.max_history:]
                
                # Log status changes
                if len(self.health_history) > 1:
                    prev_status = self.health_history[-2].status
                    if health.status != prev_status:
                        self.logger.warning(f"System status changed: {prev_status} -> {health.status}")
                
                # Log critical conditions
                if health.status == "critical":
                    self.logger.error(f"CRITICAL: CPU={health.cpu_percent:.1f}%, "
                                    f"Memory={health.memory_percent:.1f}%, "
                                    f"Threads={health.thread_count}")

                elif health.status == "warning":
                    self.logger.warning(f"WARNING: CPU={health.cpu_percent:.1f}%, "
                                      f"Memory={health.memory_percent:.1f}%, "
                                      f"Threads={health.thread_count}")

                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"Monitor loop error: {e}")
                time.sleep(interval)
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary."""
        if not self.health_history:
            return {"status": "no_data", "message": "No health data available"}
        
        recent = self.health_history[-10:]  # Last 10 readings
        current = self.health_history[-1]
        
        avg_cpu = sum(h.cpu_percent for h in recent) / len(recent)
        avg_memory = sum(h.memory_percent for h in recent) / len(recent)
        max_threads = max(h.thread_count for h in recent)
        
        return {
            "current_status": current.status,
            "current_cpu": current.cpu_percent,
            "current_memory": current.memory_percent,
            "current_threads": current.thread_count,
            "avg_cpu_10min": avg_cpu,
            "avg_memory_10min": avg_memory,
            "max_threads_10min": max_threads,
            "total_readings": len(self.health_history),
            "monitoring_duration": (current.timestamp - self.health_history[0].timestamp).total_seconds() / 60
        }


class SystemHealthChecker:
    """
    System health checker for backup operations.
    
    Provides health checks and recommendations for optimal backup performance.
    """
    
    def __init__(self):
        """Initialize health checker."""
        self.logger = logging.getLogger(f"{__name__}.SystemHealthChecker")
    
    def check_backup_readiness(self) -> Dict[str, Any]:
        """Check if system is ready for backup operations."""
        health_check = {
            "ready": True,
            "warnings": [],
            "errors": [],
            "recommendations": []
        }
        
        try:
            # Check available disk space
            disk_usage = psutil.disk_usage('.')
            free_gb = disk_usage.free / (1024**3)
            
            if free_gb < 5:
                health_check["errors"].append(f"Low disk space: {free_gb:.1f}GB free")
                health_check["ready"] = False
            elif free_gb < 10:
                health_check["warnings"].append(f"Disk space warning: {free_gb:.1f}GB free")
            
            # Check available memory
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            
            if available_gb < 1:
                health_check["errors"].append(f"Low memory: {available_gb:.1f}GB available")
                health_check["ready"] = False
            elif available_gb < 2:
                health_check["warnings"].append(f"Memory warning: {available_gb:.1f}GB available")
            
            # Check CPU load
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > BackupConstants.CPU_WARNING_THRESHOLD:
                health_check["warnings"].append(f"High CPU usage: {cpu_percent:.1f}%")
            
            # Generate recommendations
            if health_check["warnings"] or health_check["errors"]:
                health_check["recommendations"].append("Consider running backup during off-peak hours")
                health_check["recommendations"].append("Monitor system resources during backup")
            
            if not health_check["ready"]:
                health_check["recommendations"].append("Resolve critical issues before starting backup")
        
        except Exception as e:
            health_check["errors"].append(f"Health check failed: {e}")
            health_check["ready"] = False
        
        return health_check
    
    def get_performance_recommendations(self) -> List[str]:
        """Get performance optimization recommendations."""
        recommendations = []
        
        try:
            # Check system specs
            cpu_count = psutil.cpu_count()
            memory_gb = psutil.virtual_memory().total / (1024**3)
            
            # Thread recommendations
            if cpu_count >= 8:
                recommendations.append("Consider increasing max_threads to 6-8 for better performance")
            elif cpu_count <= 2:
                recommendations.append("Limit max_threads to 2-3 on this system")
            
            # Memory recommendations
            if memory_gb >= 16:
                recommendations.append("Can increase memory thresholds for better performance")
            elif memory_gb <= 4:
                recommendations.append("Use conservative memory settings on this system")
            
            # General recommendations
            recommendations.extend([
                "Enable streaming for large tables",
                "Use compression to reduce storage usage",
                "Monitor resource usage during backup",
                "Schedule backups during off-peak hours"
            ])
        
        except Exception as e:
            recommendations.append(f"Could not generate recommendations: {e}")
        
        return recommendations

    def _is_emergency_condition(self, health: SystemHealth) -> bool:
        """Check if system is in emergency condition requiring immediate action."""
        return (
            health.thread_count >= BackupConstants.EMERGENCY_THREAD_THRESHOLD or
            health.memory_percent >= BackupConstants.EMERGENCY_MEMORY_THRESHOLD or
            health.cpu_percent >= 98.0  # Near 100% CPU
        )

    def _emergency_response(self, health: SystemHealth):
        """Execute emergency response to critical resource conditions."""
        self.logger.critical(f"EMERGENCY RESPONSE TRIGGERED: "
                           f"CPU={health.cpu_percent:.1f}%, "
                           f"Memory={health.memory_percent:.1f}%, "
                           f"Threads={health.thread_count}")

        try:
            # 1. Force thread cleanup
            self._emergency_thread_cleanup()

            # 2. Force garbage collection
            import gc
            gc.collect()

            # 3. Try to get thread manager and force cleanup
            try:
                from ..core.thread_manager import get_thread_manager
                thread_manager = get_thread_manager()
                thread_manager._force_cleanup()
                self.logger.info("Emergency thread manager cleanup completed")
            except Exception as e:
                self.logger.error(f"Emergency thread manager cleanup failed: {e}")

            # 4. Log post-cleanup status
            import time
            time.sleep(2)  # Give system time to clean up
            post_health = self.get_system_health()
            self.logger.info(f"Post-emergency cleanup: "
                           f"CPU={post_health.cpu_percent:.1f}%, "
                           f"Memory={post_health.memory_percent:.1f}%, "
                           f"Threads={post_health.thread_count}")

        except Exception as e:
            self.logger.error(f"Emergency response failed: {e}")

    def _emergency_thread_cleanup(self):
        """Emergency thread cleanup - more aggressive than normal cleanup."""
        import threading
        import gc

        try:
            # Get all threads
            all_threads = threading.enumerate()
            daemon_threads = [t for t in all_threads if t.daemon and t != threading.current_thread()]

            self.logger.warning(f"Emergency cleanup: Found {len(daemon_threads)} daemon threads to clean")

            # Force garbage collection multiple times
            for _ in range(3):
                gc.collect()

            # Log thread details for debugging
            thread_names = [t.name for t in daemon_threads[:10]]  # First 10 thread names
            self.logger.info(f"Sample daemon threads: {thread_names}")

        except Exception as e:
            self.logger.error(f"Emergency thread cleanup failed: {e}")

    def apply_throttling(self, operation_context: str = "unknown"):
        """Apply smart throttling based on current system conditions."""
        current_time = time.time()

        # Check cooldown period
        if current_time - self.last_throttle_time < self.throttle_cooldown:
            self.logger.debug(f"Throttling skipped due to cooldown (operation: {operation_context})")
            return

        health = self.get_system_health()
        throttle_applied = False
        throttle_actions = []

        # Memory-based throttling
        if health.memory_percent > self.adaptive_thresholds['memory_critical']:
            throttle_actions.append("critical_memory_throttle")
            self._apply_critical_memory_throttle()
            throttle_applied = True
        elif health.memory_percent > self.adaptive_thresholds['memory_warning']:
            throttle_actions.append("memory_throttle")
            self._apply_memory_throttle()
            throttle_applied = True

        # CPU-based throttling
        if health.cpu_percent > self.adaptive_thresholds['cpu_critical']:
            throttle_actions.append("critical_cpu_throttle")
            self._apply_critical_cpu_throttle()
            throttle_applied = True
        elif health.cpu_percent > self.adaptive_thresholds['cpu_warning']:
            throttle_actions.append("cpu_throttle")
            self._apply_cpu_throttle()
            throttle_applied = True

        # Thread-based throttling
        if health.thread_count > self.thread_critical:
            throttle_actions.append("thread_throttle")
            self._apply_thread_throttle()
            throttle_applied = True

        if throttle_applied:
            self.throttling_active = True
            self.last_throttle_time = current_time

            # Record throttling event
            throttle_record = {
                'timestamp': current_time,
                'operation_context': operation_context,
                'actions': throttle_actions,
                'system_health': health
            }
            self.throttling_history.append(throttle_record)

            # Keep only recent throttling history
            if len(self.throttling_history) > 50:
                self.throttling_history = self.throttling_history[-50:]

            self.logger.warning(f"Smart throttling applied for {operation_context}: {', '.join(throttle_actions)}")

        # Adaptive threshold adjustment based on history
        self._adjust_adaptive_thresholds()

    def _apply_memory_throttle(self):
        """Apply memory-specific throttling measures."""
        import gc
        import time

        # Force garbage collection
        gc.collect()

        # Brief pause to allow memory cleanup
        time.sleep(0.5)

        self.logger.info("Applied memory throttling: garbage collection + brief pause")

    def _apply_critical_memory_throttle(self):
        """Apply critical memory throttling measures."""
        import gc
        import time

        # Aggressive garbage collection
        for _ in range(3):
            gc.collect()

        # Longer pause for memory recovery
        time.sleep(2.0)

        self.logger.warning("Applied critical memory throttling: aggressive GC + extended pause")

    def _apply_cpu_throttle(self):
        """Apply CPU-specific throttling measures."""
        import time

        # Brief pause to reduce CPU pressure
        time.sleep(1.0)

        self.logger.info("Applied CPU throttling: brief pause")

    def _apply_critical_cpu_throttle(self):
        """Apply critical CPU throttling measures."""
        import time

        # Extended pause for CPU recovery
        time.sleep(3.0)

        self.logger.warning("Applied critical CPU throttling: extended pause")

    def _apply_thread_throttle(self):
        """Apply thread-specific throttling measures."""
        import time

        # Pause to allow thread cleanup
        time.sleep(1.5)

        # Force garbage collection to clean up thread references
        import gc
        gc.collect()

        self.logger.info("Applied thread throttling: pause + garbage collection")

    def _adjust_adaptive_thresholds(self):
        """Adjust thresholds based on throttling history and system performance."""
        if len(self.throttling_history) < 5:
            return

        recent_throttles = self.throttling_history[-5:]
        throttle_frequency = len(recent_throttles) / 300  # throttles per 5 minutes

        # If throttling frequently, lower thresholds to be more aggressive
        if throttle_frequency > 0.1:  # More than 1 throttle per 50 seconds
            adjustment_factor = 0.95  # Lower thresholds by 5%

            self.adaptive_thresholds['memory_warning'] *= adjustment_factor
            self.adaptive_thresholds['cpu_warning'] *= adjustment_factor

            self.logger.info(f"Lowered adaptive thresholds due to frequent throttling (factor: {adjustment_factor})")

        # If no recent throttling, gradually raise thresholds
        elif throttle_frequency < 0.02:  # Less than 1 throttle per 250 seconds
            adjustment_factor = 1.02  # Raise thresholds by 2%

            # Don't exceed original thresholds
            self.adaptive_thresholds['memory_warning'] = min(
                self.adaptive_thresholds['memory_warning'] * adjustment_factor,
                self.memory_warning
            )
            self.adaptive_thresholds['cpu_warning'] = min(
                self.adaptive_thresholds['cpu_warning'] * adjustment_factor,
                self.cpu_warning
            )

            self.logger.debug(f"Raised adaptive thresholds due to low throttling (factor: {adjustment_factor})")

    def get_throttling_status(self) -> Dict[str, Any]:
        """Get current throttling status and statistics."""
        current_time = time.time()

        return {
            'throttling_active': self.throttling_active,
            'last_throttle_time': self.last_throttle_time,
            'time_since_last_throttle': current_time - self.last_throttle_time,
            'throttle_events_count': len(self.throttling_history),
            'adaptive_thresholds': self.adaptive_thresholds.copy(),
            'recent_throttle_frequency': len([t for t in self.throttling_history
                                            if current_time - t['timestamp'] < 300]) / 5  # per minute
        }

    def reset_throttling_state(self):
        """Reset throttling state (useful for testing or manual intervention)."""
        self.throttling_active = False
        self.last_throttle_time = 0
        self.throttling_history.clear()

        # Reset adaptive thresholds to original values
        self.adaptive_thresholds = {
            'memory_warning': self.memory_warning,
            'memory_critical': self.memory_critical,
            'cpu_warning': self.cpu_warning,
            'cpu_critical': self.cpu_critical
        }

        self.logger.info("Throttling state reset to defaults")
