#!/usr/bin/env python3
"""
TNGD Backup System - Performance Dashboard

Real-time performance monitoring dashboard that displays:
- System resource usage
- Backup operation performance
- Optimization status and recommendations
- Performance trends and analytics

This dashboard provides a comprehensive view of your backup system's performance
and optimization effectiveness.
"""

import os
import sys
import time
import json
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from tngd_backup.core.performance_optimizer import PerformanceOptimizer
    from tngd_backup.utils.performance_tracker import PerformanceTracker
    from tngd_backup.utils.monitoring import ResourceMonitor
    OPTIMIZATION_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Optimization modules not available: {e}")
    OPTIMIZATION_AVAILABLE = False


class PerformanceDashboard:
    """
    Real-time performance monitoring dashboard.
    
    Provides a comprehensive view of backup system performance,
    optimization status, and recommendations.
    """
    
    def __init__(self, refresh_interval: int = 30):
        """Initialize the performance dashboard."""
        self.refresh_interval = refresh_interval
        self.logger = logging.getLogger(__name__)
        
        # Initialize components if available
        self.optimizer = None
        self.tracker = None
        self.monitor = None
        
        if OPTIMIZATION_AVAILABLE:
            try:
                self.optimizer = PerformanceOptimizer()
                self.tracker = PerformanceTracker()
                self.monitor = ResourceMonitor()
                self.logger.info("Performance dashboard initialized with optimization features")
            except Exception as e:
                self.logger.warning(f"Failed to initialize optimization components: {e}")
        
        # Dashboard state
        self.last_update = 0
        self.dashboard_data = {}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status and health."""
        status = {
            'timestamp': datetime.now().isoformat(),
            'optimization_available': OPTIMIZATION_AVAILABLE,
            'components_status': {
                'optimizer': self.optimizer is not None,
                'tracker': self.tracker is not None,
                'monitor': self.monitor is not None
            }
        }
        
        if self.monitor:
            try:
                health = self.monitor.get_system_health()
                status['system_health'] = {
                    'cpu_percent': health.cpu_percent,
                    'memory_percent': health.memory_percent,
                    'memory_mb': health.memory_mb,
                    'status': health.status,
                    'thread_count': health.thread_count
                }
                
                # Get throttling status
                throttling = self.monitor.get_throttling_status()
                status['throttling'] = {
                    'active': throttling.get('throttling_active', False),
                    'recent_frequency': throttling.get('recent_throttle_frequency', 0),
                    'time_since_last': throttling.get('time_since_last_throttle', 0)
                }
                
            except Exception as e:
                status['system_health'] = {'error': str(e)}
                status['throttling'] = {'error': str(e)}
        
        return status
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary and analysis."""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'available': self.optimizer is not None
        }
        
        if self.optimizer:
            try:
                # Get comprehensive analysis
                analysis = self.optimizer.get_performance_analysis()
                
                # Extract key metrics
                operation_analyses = analysis.get('operation_analyses', {})
                
                if operation_analyses:
                    # Calculate overall metrics
                    total_operations = sum(op.total_operations for op in operation_analyses.values())
                    avg_success_rate = sum(op.success_rate for op in operation_analyses.values()) / len(operation_analyses)
                    avg_duration = sum(op.avg_duration for op in operation_analyses.values()) / len(operation_analyses)
                    avg_throughput = sum(op.avg_throughput for op in operation_analyses.values()) / len(operation_analyses)
                    
                    summary['overall_metrics'] = {
                        'total_operations': total_operations,
                        'avg_success_rate': avg_success_rate,
                        'avg_duration': avg_duration,
                        'avg_throughput': avg_throughput,
                        'operations_count': len(operation_analyses)
                    }
                    
                    # Get top performing and problematic operations
                    sorted_ops = sorted(operation_analyses.items(), 
                                      key=lambda x: x[1].success_rate, reverse=True)
                    
                    summary['top_operations'] = [
                        {
                            'name': name,
                            'success_rate': op.success_rate,
                            'avg_duration': op.avg_duration,
                            'trend': op.trend
                        }
                        for name, op in sorted_ops[:5]
                    ]
                    
                    summary['problematic_operations'] = [
                        {
                            'name': name,
                            'success_rate': op.success_rate,
                            'avg_duration': op.avg_duration,
                            'bottlenecks': op.bottlenecks
                        }
                        for name, op in sorted_ops[-3:] if op.success_rate < 0.9
                    ]
                
                # Get system recommendations
                summary['recommendations'] = analysis.get('system_recommendations', [])
                
                # Get optimization status
                summary['optimization_status'] = self.optimizer.get_optimization_summary()
                
            except Exception as e:
                summary['error'] = str(e)
        
        return summary
    
    def get_recent_activity(self, hours: int = 24) -> Dict[str, Any]:
        """Get recent backup activity and performance."""
        activity = {
            'timestamp': datetime.now().isoformat(),
            'period_hours': hours,
            'available': self.tracker is not None
        }
        
        if self.tracker:
            try:
                # Export recent metrics
                start_time = time.time() - (hours * 3600)
                metrics = self.tracker.export_metrics(start_time=start_time)
                
                if metrics:
                    # Group by operation
                    operations = {}
                    for metric in metrics:
                        op_name = metric['operation']
                        if op_name not in operations:
                            operations[op_name] = []
                        operations[op_name].append(metric)
                    
                    # Calculate activity summary
                    activity['operations'] = {}
                    for op_name, op_metrics in operations.items():
                        successful = [m for m in op_metrics if m['success']]
                        
                        activity['operations'][op_name] = {
                            'total_runs': len(op_metrics),
                            'successful_runs': len(successful),
                            'success_rate': len(successful) / len(op_metrics) if op_metrics else 0,
                            'avg_duration': sum(m['duration'] for m in successful) / len(successful) if successful else 0,
                            'avg_throughput': sum(m['throughput'] for m in successful) / len(successful) if successful else 0,
                            'last_run': max(m['timestamp'] for m in op_metrics) if op_metrics else 0
                        }
                    
                    # Overall activity stats
                    activity['summary'] = {
                        'total_operations': len(metrics),
                        'unique_operation_types': len(operations),
                        'overall_success_rate': len([m for m in metrics if m['success']]) / len(metrics) if metrics else 0,
                        'time_range': {
                            'start': min(m['timestamp'] for m in metrics) if metrics else 0,
                            'end': max(m['timestamp'] for m in metrics) if metrics else 0
                        }
                    }
                
            except Exception as e:
                activity['error'] = str(e)
        
        return activity
    
    def format_dashboard_output(self) -> str:
        """Format dashboard data for console display."""
        output = []
        
        # Header
        output.append("=" * 80)
        output.append("🚀 TNGD Backup System - Performance Dashboard")
        output.append("=" * 80)
        output.append(f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output.append("")
        
        # System Status
        system_status = self.get_system_status()
        output.append("📊 System Status")
        output.append("-" * 40)
        
        if system_status.get('system_health'):
            health = system_status['system_health']
            status_icon = "🟢" if health.get('status') == 'healthy' else "🟡" if health.get('status') == 'warning' else "🔴"
            output.append(f"{status_icon} System Health: {health.get('status', 'unknown').upper()}")
            output.append(f"   CPU Usage: {health.get('cpu_percent', 0):.1f}%")
            output.append(f"   Memory Usage: {health.get('memory_percent', 0):.1f}% ({health.get('memory_mb', 0):.0f} MB)")
            output.append(f"   Active Threads: {health.get('thread_count', 0)}")
            
            # Throttling status
            throttling = system_status.get('throttling', {})
            if throttling.get('active'):
                output.append("⚠️  Throttling: ACTIVE")
            else:
                output.append("✅ Throttling: Inactive")
        else:
            output.append("❌ System monitoring unavailable")
        
        output.append("")
        
        # Performance Summary
        perf_summary = self.get_performance_summary()
        output.append("📈 Performance Summary")
        output.append("-" * 40)
        
        if perf_summary.get('overall_metrics'):
            metrics = perf_summary['overall_metrics']
            output.append(f"Total Operations: {metrics['total_operations']}")
            output.append(f"Average Success Rate: {metrics['avg_success_rate']:.1%}")
            output.append(f"Average Duration: {metrics['avg_duration']:.1f}s")
            output.append(f"Average Throughput: {metrics['avg_throughput']:.0f} rows/s")
            
            # Top operations
            if perf_summary.get('top_operations'):
                output.append("\n🏆 Top Performing Operations:")
                for op in perf_summary['top_operations'][:3]:
                    trend_icon = "📈" if op['trend'] == 'improving' else "📉" if op['trend'] == 'degrading' else "➡️"
                    output.append(f"   {trend_icon} {op['name']}: {op['success_rate']:.1%} success, {op['avg_duration']:.1f}s")
            
            # Problematic operations
            if perf_summary.get('problematic_operations'):
                output.append("\n⚠️  Operations Needing Attention:")
                for op in perf_summary['problematic_operations']:
                    output.append(f"   🔴 {op['name']}: {op['success_rate']:.1%} success")
                    if op['bottlenecks']:
                        output.append(f"      Issues: {', '.join(op['bottlenecks'][:2])}")
        else:
            output.append("No performance data available")
        
        output.append("")
        
        # Optimization Status
        if perf_summary.get('optimization_status'):
            opt_status = perf_summary['optimization_status']
            output.append("⚙️  Optimization Features")
            output.append("-" * 40)
            
            features = [
                ('Performance Tracking', opt_status.get('performance_tracking', {}).get('enabled', False)),
                ('Progressive Chunk Sizing', opt_status.get('progressive_chunk_sizing', {}).get('enabled', False)),
                ('Adaptive Compression', opt_status.get('adaptive_compression', {}).get('enabled', False)),
                ('Smart Throttling', opt_status.get('smart_throttling', {}).get('enabled', False)),
                ('Smart Retry', opt_status.get('smart_retry', {}).get('enabled', False))
            ]
            
            for feature, enabled in features:
                status_icon = "✅" if enabled else "❌"
                output.append(f"{status_icon} {feature}")
        
        output.append("")
        
        # Recommendations
        if perf_summary.get('recommendations'):
            output.append("💡 Optimization Recommendations")
            output.append("-" * 40)
            for i, rec in enumerate(perf_summary['recommendations'][:5], 1):
                output.append(f"{i}. {rec}")
        else:
            output.append("💡 No optimization recommendations at this time")
        
        output.append("")
        
        # Recent Activity
        activity = self.get_recent_activity(hours=6)  # Last 6 hours
        output.append("🕒 Recent Activity (Last 6 Hours)")
        output.append("-" * 40)
        
        if activity.get('summary'):
            summary = activity['summary']
            output.append(f"Total Operations: {summary['total_operations']}")
            output.append(f"Operation Types: {summary['unique_operation_types']}")
            output.append(f"Success Rate: {summary['overall_success_rate']:.1%}")
            
            if activity.get('operations'):
                output.append("\nRecent Operations:")
                for op_name, op_data in list(activity['operations'].items())[:5]:
                    last_run = datetime.fromtimestamp(op_data['last_run']).strftime('%H:%M')
                    output.append(f"   • {op_name}: {op_data['success_rate']:.1%} success, last run {last_run}")
        else:
            output.append("No recent activity data available")
        
        output.append("")
        output.append("=" * 80)
        output.append(f"Dashboard will refresh in {self.refresh_interval} seconds...")
        output.append("Press Ctrl+C to exit")
        
        return "\n".join(output)
    
    def run_dashboard(self, continuous: bool = True):
        """Run the performance dashboard."""
        try:
            while True:
                # Clear screen (works on most terminals)
                os.system('cls' if os.name == 'nt' else 'clear')
                
                # Display dashboard
                dashboard_output = self.format_dashboard_output()
                print(dashboard_output)
                
                if not continuous:
                    break
                
                # Wait for refresh interval
                time.sleep(self.refresh_interval)
                
        except KeyboardInterrupt:
            print("\n\n👋 Dashboard stopped by user")
        except Exception as e:
            print(f"\n❌ Dashboard error: {e}")
    
    def export_dashboard_data(self, output_file: str = None) -> str:
        """Export dashboard data to JSON file."""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"data/logs/dashboard_export_{timestamp}.json"
        
        # Collect all dashboard data
        dashboard_data = {
            'export_timestamp': datetime.now().isoformat(),
            'system_status': self.get_system_status(),
            'performance_summary': self.get_performance_summary(),
            'recent_activity': self.get_recent_activity(hours=24)
        }
        
        # Ensure output directory exists
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        # Save to file
        with open(output_file, 'w') as f:
            json.dump(dashboard_data, f, indent=2, default=str)
        
        return output_file


def main():
    """Main dashboard function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='TNGD Backup Performance Dashboard')
    parser.add_argument('--refresh', type=int, default=30, help='Refresh interval in seconds')
    parser.add_argument('--once', action='store_true', help='Show dashboard once and exit')
    parser.add_argument('--export', help='Export dashboard data to JSON file')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.WARNING
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create dashboard
    dashboard = PerformanceDashboard(refresh_interval=args.refresh)
    
    if args.export:
        # Export mode
        output_file = dashboard.export_dashboard_data(args.export)
        print(f"📄 Dashboard data exported to: {output_file}")
        return 0
    
    # Display mode
    if not OPTIMIZATION_AVAILABLE:
        print("⚠️  Warning: Optimization features not available.")
        print("   Run 'python scripts/enable_optimizations.py' to enable them.")
        print()
    
    dashboard.run_dashboard(continuous=not args.once)
    return 0


if __name__ == "__main__":
    sys.exit(main())
