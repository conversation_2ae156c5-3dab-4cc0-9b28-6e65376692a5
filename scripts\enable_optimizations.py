#!/usr/bin/env python3
"""
Enable Backup System Optimizations

This script enables and configures the new performance optimization features
for the TNGD backup system. It provides a guided setup process to configure
all optimization components.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from tngd_backup.core.performance_optimizer import PerformanceOptimizer
from tngd_backup.utils.monitoring import ResourceMonitor
from tngd_backup.utils.performance_tracker import PerformanceTracker


def setup_logging():
    """Setup logging for the optimization setup."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('data/logs/optimization_setup.log')
        ]
    )
    return logging.getLogger(__name__)


def check_system_requirements():
    """Check if system meets requirements for optimizations."""
    logger = logging.getLogger(__name__)
    requirements_met = True
    
    # Check Python version
    if sys.version_info < (3, 7):
        logger.error("Python 3.7 or higher required")
        requirements_met = False
    
    # Check for required packages
    try:
        import psutil
        logger.info("✓ psutil available - system monitoring enabled")
    except ImportError:
        logger.warning("⚠ psutil not available - system monitoring will be limited")
    
    # Check disk space
    try:
        import shutil
        free_space_gb = shutil.disk_usage('.').free / (1024**3)
        if free_space_gb < 5:
            logger.warning(f"⚠ Low disk space: {free_space_gb:.1f}GB free")
        else:
            logger.info(f"✓ Sufficient disk space: {free_space_gb:.1f}GB free")
    except Exception as e:
        logger.warning(f"Could not check disk space: {e}")
    
    return requirements_met


def create_optimization_config():
    """Create or update optimization configuration."""
    logger = logging.getLogger(__name__)
    
    config_path = Path("config/performance_optimization.json")
    
    if config_path.exists():
        logger.info("Performance optimization config already exists")
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        logger.info("Creating new performance optimization config")
        # Use the default config from the file we created
        config = {
            "version": "1.0",
            "description": "TNGD Backup System - Performance Optimization Configuration",
            "performance_tracking": {"enabled": True},
            "progressive_chunk_sizing": {"enabled": True},
            "adaptive_compression": {"enabled": True},
            "smart_throttling": {"enabled": True},
            "smart_retry": {"enabled": True}
        }
    
    # Ensure all optimization features are enabled
    config["performance_tracking"]["enabled"] = True
    config["progressive_chunk_sizing"]["enabled"] = True
    config["adaptive_compression"]["enabled"] = True
    config["smart_throttling"]["enabled"] = True
    config["smart_retry"]["enabled"] = True
    
    # Save updated config
    config_path.parent.mkdir(parents=True, exist_ok=True)
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    logger.info(f"Optimization config saved to {config_path}")
    return config


def test_optimization_components():
    """Test that all optimization components are working."""
    logger = logging.getLogger(__name__)
    
    try:
        # Test Performance Optimizer
        logger.info("Testing Performance Optimizer...")
        optimizer = PerformanceOptimizer()
        summary = optimizer.get_optimization_summary()
        logger.info(f"✓ Performance Optimizer initialized: {len(summary)} features")
        
        # Test Resource Monitor
        logger.info("Testing Resource Monitor...")
        monitor = ResourceMonitor()
        health = monitor.get_system_health()
        logger.info(f"✓ Resource Monitor working: {health.status}")
        
        # Test Performance Tracker
        logger.info("Testing Performance Tracker...")
        tracker = PerformanceTracker()
        tracker.record_operation("test_operation", 1.0, 1000, True)
        logger.info("✓ Performance Tracker working")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Component test failed: {e}")
        return False


def update_backup_configs():
    """Update existing backup configurations to use optimizations."""
    logger = logging.getLogger(__name__)
    
    # Update large dataset config
    large_config_path = Path("config/large_dataset.json")
    if large_config_path.exists():
        with open(large_config_path, 'r') as f:
            config = json.load(f)
        
        # Enable optimization features
        config["performance_optimizations"] = config.get("performance_optimizations", {})
        config["performance_optimizations"]["enable_adaptive_optimization"] = True
        config["performance_optimizations"]["enable_performance_tracking"] = True
        
        # Update streaming config for progressive sizing
        if "streaming_config" in config:
            config["streaming_config"]["enable_progressive_sizing"] = True
            config["streaming_config"]["enable_smart_retry"] = True
        
        with open(large_config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info("✓ Updated large dataset configuration")
    
    # Update other configs as needed
    for config_file in ["config/default.json", "config/production.json"]:
        config_path = Path(config_file)
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                
                # Add optimization settings
                config["enable_performance_optimization"] = True
                
                with open(config_path, 'w') as f:
                    json.dump(config, f, indent=2)
                
                logger.info(f"✓ Updated {config_file}")
            except Exception as e:
                logger.warning(f"Could not update {config_file}: {e}")


def create_optimization_scripts():
    """Create helper scripts for managing optimizations."""
    logger = logging.getLogger(__name__)
    
    scripts_dir = Path("scripts/optimization")
    scripts_dir.mkdir(parents=True, exist_ok=True)
    
    # Create performance monitoring script
    monitor_script = scripts_dir / "monitor_performance.py"
    with open(monitor_script, 'w') as f:
        f.write('''#!/usr/bin/env python3
"""Monitor backup performance in real-time."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from tngd_backup.core.performance_optimizer import PerformanceOptimizer

def main():
    optimizer = PerformanceOptimizer()
    analysis = optimizer.get_performance_analysis()
    
    print("=== BACKUP PERFORMANCE ANALYSIS ===")
    for operation, data in analysis.get('operation_analyses', {}).items():
        print(f"\\nOperation: {operation}")
        print(f"  Success Rate: {data.success_rate:.1%}")
        print(f"  Avg Duration: {data.avg_duration:.1f}s")
        print(f"  Avg Throughput: {data.avg_throughput:.0f} rows/s")
        print(f"  Trend: {data.trend}")
        if data.recommendations:
            print(f"  Recommendations: {', '.join(data.recommendations[:2])}")

if __name__ == "__main__":
    main()
''')
    
    # Create optimization toggle script
    toggle_script = scripts_dir / "toggle_optimization.py"
    with open(toggle_script, 'w') as f:
        f.write('''#!/usr/bin/env python3
"""Toggle optimization features on/off."""
import sys
import argparse
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from tngd_backup.core.performance_optimizer import PerformanceOptimizer

def main():
    parser = argparse.ArgumentParser(description='Toggle optimization features')
    parser.add_argument('action', choices=['enable', 'disable'], help='Action to perform')
    parser.add_argument('--feature', help='Specific feature to toggle (optional)')
    args = parser.parse_args()
    
    optimizer = PerformanceOptimizer()
    
    if args.action == 'enable':
        optimizer.enable_optimization(args.feature)
        print(f"Enabled optimization: {args.feature or 'all features'}")
    else:
        optimizer.disable_optimization(args.feature)
        print(f"Disabled optimization: {args.feature or 'all features'}")
    
    # Show current status
    summary = optimizer.get_optimization_summary()
    print("\\nCurrent optimization status:")
    for feature, status in summary.items():
        if isinstance(status, dict) and 'enabled' in status:
            print(f"  {feature}: {'✓' if status['enabled'] else '✗'}")

if __name__ == "__main__":
    main()
''')
    
    # Make scripts executable
    monitor_script.chmod(0o755)
    toggle_script.chmod(0o755)
    
    logger.info(f"✓ Created optimization scripts in {scripts_dir}")


def main():
    """Main setup function."""
    print("🚀 TNGD Backup System - Performance Optimization Setup")
    print("=" * 60)
    
    logger = setup_logging()
    
    # Check system requirements
    print("\n1. Checking system requirements...")
    if not check_system_requirements():
        print("❌ System requirements not met. Please address the issues above.")
        return 1
    
    # Create optimization configuration
    print("\n2. Setting up optimization configuration...")
    config = create_optimization_config()
    
    # Test components
    print("\n3. Testing optimization components...")
    if not test_optimization_components():
        print("❌ Component tests failed. Check the logs for details.")
        return 1
    
    # Update existing configs
    print("\n4. Updating backup configurations...")
    update_backup_configs()
    
    # Create helper scripts
    print("\n5. Creating optimization management scripts...")
    create_optimization_scripts()
    
    print("\n✅ Performance optimization setup complete!")
    print("\nNext steps:")
    print("1. Run your backup with: python backup.py --production")
    print("2. Monitor performance with: python scripts/optimization/monitor_performance.py")
    print("3. View real-time dashboard: python scripts/performance_dashboard.py")
    print("\nOptimization features enabled:")
    print("  ✓ Progressive chunk sizing")
    print("  ✓ Adaptive compression")
    print("  ✓ Smart throttling")
    print("  ✓ Intelligent retry logic")
    print("  ✓ Performance tracking")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
