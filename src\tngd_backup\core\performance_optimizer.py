#!/usr/bin/env python3
"""
Performance Optimization Manager

This module integrates all performance optimization features including:
- Progressive chunk sizing
- Adaptive compression
- Smart throttling
- Intelligent retry logic
- Performance tracking and recommendations

It provides a unified interface for managing and optimizing backup performance.
"""

import json
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# Import optimization components
from ..utils.performance_tracker import PerformanceTracker
from ..utils.monitoring import ResourceMonitor
from ..utils.error_handler import <PERSON>rror<PERSON>and<PERSON>
from ..core.compression_service import CompressionService


class PerformanceOptimizer:
    """
    Unified performance optimization manager.
    
    Coordinates all performance optimization features and provides
    intelligent recommendations based on real-time analysis.
    """
    
    def __init__(self, config_path: str = "config/performance_optimization.json"):
        """Initialize performance optimizer."""
        self.logger = logging.getLogger(f"{__name__}.PerformanceOptimizer")
        
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Initialize components
        self.performance_tracker = PerformanceTracker(
            max_history=self.config.get('performance_tracking', {}).get('max_history_entries', 1000)
        )
        
        self.resource_monitor = ResourceMonitor() if self._is_monitoring_enabled() else None
        self.error_handler = ErrorHandler(self.logger)
        self.compression_service = CompressionService()
        
        # Optimization state
        self.optimization_active = True
        self.last_optimization_time = 0
        self.optimization_interval = self.config.get('monitoring_intervals', {}).get('recommendation_update_seconds', 600)
        
        # Performance baselines
        self.performance_baselines = {}
        self.baseline_established = False
        
        self.logger.info("Performance optimizer initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load performance optimization configuration."""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            self.logger.info(f"Loaded performance configuration from {config_path}")
            return config
        except Exception as e:
            self.logger.warning(f"Failed to load config from {config_path}: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default performance optimization configuration."""
        return {
            "performance_tracking": {"enabled": True, "max_history_entries": 1000},
            "progressive_chunk_sizing": {"enabled": True},
            "adaptive_compression": {"enabled": True},
            "smart_throttling": {"enabled": True},
            "smart_retry": {"enabled": True}
        }
    
    def _is_monitoring_enabled(self) -> bool:
        """Check if resource monitoring is enabled."""
        return self.config.get('performance_tracking', {}).get('enable_system_metrics', True)
    
    def optimize_chunk_size(self, operation: str, current_size: int, 
                           performance_data: Dict[str, Any]) -> int:
        """
        Optimize chunk size based on performance data.
        
        Args:
            operation: Name of the operation
            current_size: Current chunk size
            performance_data: Recent performance metrics
            
        Returns:
            Optimized chunk size
        """
        if not self.config.get('progressive_chunk_sizing', {}).get('enabled', True):
            return current_size
        
        config = self.config.get('progressive_chunk_sizing', {})
        
        # Get performance thresholds
        performance_threshold = config.get('performance_threshold_seconds', 30.0)
        memory_threshold = config.get('memory_pressure_threshold_percent', 80.0)
        
        # Analyze recent performance
        recent_duration = performance_data.get('avg_duration', 0)
        memory_pressure = performance_data.get('memory_pressure', 0)
        success_rate = performance_data.get('success_rate', 1.0)
        
        # Determine optimization direction
        if success_rate < 0.9:  # Poor success rate
            new_size = int(current_size * config.get('size_decrease_factor', 0.8))
            self.logger.info(f"Reducing chunk size due to poor success rate: {current_size} -> {new_size}")
        
        elif memory_pressure > memory_threshold:  # High memory pressure
            new_size = int(current_size * config.get('size_decrease_factor', 0.8))
            self.logger.info(f"Reducing chunk size due to memory pressure: {current_size} -> {new_size}")
        
        elif recent_duration > performance_threshold:  # Slow performance
            new_size = int(current_size * config.get('size_decrease_factor', 0.8))
            self.logger.info(f"Reducing chunk size due to slow performance: {current_size} -> {new_size}")
        
        elif (recent_duration < performance_threshold * 0.5 and 
              memory_pressure < memory_threshold * 0.8 and 
              success_rate > 0.95):  # Good performance
            new_size = int(current_size * config.get('size_increase_factor', 1.2))
            self.logger.info(f"Increasing chunk size due to good performance: {current_size} -> {new_size}")
        
        else:
            new_size = current_size  # No change
        
        # Apply bounds
        min_size = config.get('min_chunk_size', 5000)
        max_size = config.get('max_chunk_size', 200000)
        new_size = max(min_size, min(max_size, new_size))
        
        return new_size
    
    def optimize_compression(self, data_characteristics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize compression settings based on data characteristics.
        
        Args:
            data_characteristics: Data size, type, and compressibility info
            
        Returns:
            Optimized compression settings
        """
        if not self.config.get('adaptive_compression', {}).get('enabled', True):
            return {'algorithm': 'tar.gz', 'level': 3}
        
        config = self.config.get('adaptive_compression', {})
        
        size_mb = data_characteristics.get('size_mb', 0)
        compressibility = data_characteristics.get('compressibility', 'medium')
        
        # Get size thresholds
        small_threshold = config.get('size_thresholds', {}).get('small_file_mb', 100)
        large_threshold = config.get('size_thresholds', {}).get('large_file_mb', 1000)
        
        # Get compression levels
        levels = config.get('compression_levels', {'fast': 1, 'balanced': 3, 'maximum': 6})
        
        # Select compression level
        if size_mb > large_threshold:
            level = levels.get('fast', 1)  # Fast compression for large files
        elif size_mb > small_threshold:
            if compressibility == 'high':
                level = levels.get('balanced', 3)
            else:
                level = levels.get('fast', 1)
        else:  # Small files
            if compressibility == 'high':
                level = levels.get('maximum', 6)
            else:
                level = levels.get('balanced', 3)
        
        return {
            'algorithm': 'tar.gz',
            'level': level,
            'reasoning': f"Size: {size_mb}MB, Compressibility: {compressibility}"
        }
    
    def should_apply_throttling(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Determine if throttling should be applied based on system state.
        
        Returns:
            Tuple of (should_throttle, throttle_actions)
        """
        if not self.config.get('smart_throttling', {}).get('enabled', True):
            return False, {}
        
        if not self.resource_monitor:
            return False, {}
        
        try:
            health = self.resource_monitor.get_system_health()
            config = self.config.get('smart_throttling', {})
            
            # Get thresholds
            memory_warning = config.get('memory_warning_threshold_percent', 75.0)
            memory_critical = config.get('memory_critical_threshold_percent', 90.0)
            cpu_warning = config.get('cpu_warning_threshold_percent', 80.0)
            cpu_critical = config.get('cpu_critical_threshold_percent', 95.0)
            
            throttle_actions = {}
            
            # Check memory pressure
            if health.memory_percent > memory_critical:
                throttle_actions['memory'] = 'critical'
            elif health.memory_percent > memory_warning:
                throttle_actions['memory'] = 'warning'
            
            # Check CPU pressure
            if health.cpu_percent > cpu_critical:
                throttle_actions['cpu'] = 'critical'
            elif health.cpu_percent > cpu_warning:
                throttle_actions['cpu'] = 'warning'
            
            should_throttle = bool(throttle_actions)
            
            if should_throttle:
                self.logger.warning(f"Throttling recommended: {throttle_actions}")
            
            return should_throttle, throttle_actions
            
        except Exception as e:
            self.logger.debug(f"Failed to check throttling conditions: {e}")
            return False, {}
    
    def get_retry_strategy(self, error: Exception, operation: str) -> Dict[str, Any]:
        """
        Get optimized retry strategy for the given error.
        
        Args:
            error: The exception that occurred
            operation: Name of the operation
            
        Returns:
            Retry strategy configuration
        """
        if not self.config.get('smart_retry', {}).get('enabled', True):
            return {'max_retries': 3, 'base_delay': 5, 'backoff_factor': 2}
        
        # Use error handler to classify and get strategy
        error_category = self.error_handler.classify_error(error)
        strategies = self.config.get('smart_retry', {}).get('strategies', {})
        
        strategy = strategies.get(error_category, strategies.get('default', {
            'max_retries': 3, 'base_delay_seconds': 5, 'backoff_factor': 2.0
        }))
        
        # Add operation context
        strategy['error_category'] = error_category
        strategy['operation'] = operation
        
        return strategy
    
    def record_operation_performance(self, operation: str, duration: float,
                                   throughput: float = 0, success: bool = True,
                                   metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record performance metrics for an operation."""
        if self.config.get('performance_tracking', {}).get('enabled', True):
            self.performance_tracker.record_operation(
                operation, duration, throughput, success, metadata
            )
    
    def get_performance_analysis(self, operation: str = None) -> Dict[str, Any]:
        """Get comprehensive performance analysis."""
        if not self.config.get('performance_tracking', {}).get('enabled', True):
            return {"message": "Performance tracking disabled"}
        
        # Get performance analysis
        analyses = self.performance_tracker.analyze_performance(operation)
        
        # Get system recommendations
        system_recommendations = self.performance_tracker.get_system_recommendations()
        
        # Get throttling status if available
        throttling_status = {}
        if self.resource_monitor:
            throttling_status = self.resource_monitor.get_throttling_status()
        
        # Get compression recommendations
        compression_recommendations = []
        if hasattr(self.compression_service, 'get_compression_recommendations'):
            compression_recommendations = self.compression_service.get_compression_recommendations()
        
        return {
            'timestamp': time.time(),
            'operation_analyses': analyses,
            'system_recommendations': system_recommendations,
            'throttling_status': throttling_status,
            'compression_recommendations': compression_recommendations,
            'optimization_active': self.optimization_active
        }
    
    def apply_optimizations(self, operation_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply all available optimizations based on current context.
        
        Args:
            operation_context: Context information about the current operation
            
        Returns:
            Applied optimizations and their effects
        """
        optimizations_applied = {}
        
        # Check if it's time for optimization
        current_time = time.time()
        if current_time - self.last_optimization_time < self.optimization_interval:
            return {"message": "Optimization interval not reached"}
        
        try:
            # Apply throttling if needed
            should_throttle, throttle_actions = self.should_apply_throttling()
            if should_throttle and self.resource_monitor:
                self.resource_monitor.apply_throttling(operation_context.get('operation', 'unknown'))
                optimizations_applied['throttling'] = throttle_actions
            
            # Optimize chunk size if context provided
            if 'chunk_size' in operation_context and 'performance_data' in operation_context:
                old_size = operation_context['chunk_size']
                new_size = self.optimize_chunk_size(
                    operation_context.get('operation', 'unknown'),
                    old_size,
                    operation_context['performance_data']
                )
                if new_size != old_size:
                    optimizations_applied['chunk_size'] = {'old': old_size, 'new': new_size}
            
            # Optimize compression if data characteristics provided
            if 'data_characteristics' in operation_context:
                compression_settings = self.optimize_compression(operation_context['data_characteristics'])
                optimizations_applied['compression'] = compression_settings
            
            self.last_optimization_time = current_time
            
            if optimizations_applied:
                self.logger.info(f"Applied optimizations: {list(optimizations_applied.keys())}")
            
            return optimizations_applied
            
        except Exception as e:
            self.logger.error(f"Failed to apply optimizations: {e}")
            return {"error": str(e)}
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of all optimization features and their status."""
        return {
            'performance_tracking': {
                'enabled': self.config.get('performance_tracking', {}).get('enabled', False),
                'metrics_count': len(self.performance_tracker.metrics) if self.performance_tracker else 0
            },
            'progressive_chunk_sizing': {
                'enabled': self.config.get('progressive_chunk_sizing', {}).get('enabled', False)
            },
            'adaptive_compression': {
                'enabled': self.config.get('adaptive_compression', {}).get('enabled', False)
            },
            'smart_throttling': {
                'enabled': self.config.get('smart_throttling', {}).get('enabled', False),
                'resource_monitor_available': self.resource_monitor is not None
            },
            'smart_retry': {
                'enabled': self.config.get('smart_retry', {}).get('enabled', False)
            },
            'optimization_active': self.optimization_active,
            'last_optimization_time': self.last_optimization_time
        }
    
    def enable_optimization(self, feature: str = None) -> None:
        """Enable optimization features."""
        if feature:
            if feature in self.config:
                self.config[feature]['enabled'] = True
                self.logger.info(f"Enabled optimization feature: {feature}")
        else:
            self.optimization_active = True
            self.logger.info("Enabled all optimizations")
    
    def disable_optimization(self, feature: str = None) -> None:
        """Disable optimization features."""
        if feature:
            if feature in self.config:
                self.config[feature]['enabled'] = False
                self.logger.info(f"Disabled optimization feature: {feature}")
        else:
            self.optimization_active = False
            self.logger.info("Disabled all optimizations")
