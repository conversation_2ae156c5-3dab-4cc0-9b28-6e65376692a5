#!/usr/bin/env python3
"""
TNGD Backup System - Main Runner Script

Professional wrapper script for running the TNGD backup system with
proper error handling, monitoring, and user-friendly interface.

The system performs complete data backup for all configured tables,
processing all available data with optimized resource management.

Usage:
    python run_backup.py                    # Complete backup with default config
    python run_backup.py --production       # Complete backup with production config
    python run_backup.py --config FILE      # Complete backup with custom config
"""

import os
import sys
import subprocess
import time
import signal
from datetime import datetime
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))


class BackupRunner:
    """Professional backup runner with monitoring and error handling."""

    def __init__(self):
        """Initialize backup runner."""
        self.project_root = project_root
        self.monitor_process = None
        self.backup_process = None

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        print(f"\n[STOP] Received signal {signum}, shutting down gracefully...")
        self._cleanup_processes()
        sys.exit(0)

    def check_prerequisites(self):
        """Check if all required files and dependencies exist."""
        print("[CHECK] Checking prerequisites...")

        required_files = [
            "src/tngd_backup/main.py",
            "src/tngd_backup/core/backup_engine.py",
            "config/default.json"
        ]

        missing_files = []
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)

        if missing_files:
            print("[ERROR] Missing required files:")
            for file_path in missing_files:
                print(f"   - {file_path}")
            return False

        print("[OK] All required files found")
        return True

    def start_monitoring(self):
        """Start resource monitoring in background."""
        try:
            print("[MONITOR] Starting resource monitoring...")

            monitor_script = self.project_root / "src" / "tngd_backup" / "utils" / "monitoring.py"
            if monitor_script.exists():
                self.monitor_process = subprocess.Popen(
                    [sys.executable, str(monitor_script), "--monitor", "--interval", "30"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=str(self.project_root)
                )
                print(f"[OK] Resource monitor started (PID: {self.monitor_process.pid})")
            else:
                print("[WARN] Resource monitor script not found, continuing without monitoring")

        except Exception as e:
            print(f"[WARN] Could not start resource monitor: {e}")

    def run_backup(self, backup_args):
        """Run the backup system."""
        try:
            print("[START] Starting TNGD Backup System v2.0...")
            print("[INFO] Mode: COMPLETE DATA BACKUP (all tables, all available data)")

            # Build command to run the main backup module
            cmd = [
                sys.executable,
                "-m", "tngd_backup.main"
            ] + backup_args

            print(f"Command: {' '.join(cmd)}")
            print("=" * 60)

            # Set up environment with proper Python path
            env = os.environ.copy()
            src_path = str(self.project_root / "src")
            if "PYTHONPATH" in env:
                env["PYTHONPATH"] = f"{src_path}{os.pathsep}{env['PYTHONPATH']}"
            else:
                env["PYTHONPATH"] = src_path

            # Run backup with real-time output
            self.backup_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=str(self.project_root),
                env=env
            )

            # Stream output in real-time
            for line in iter(self.backup_process.stdout.readline, ''):
                print(line.rstrip())

            # Wait for completion
            return_code = self.backup_process.wait()

            print("=" * 60)
            if return_code == 0:
                print("[SUCCESS] Backup completed successfully!")
            else:
                print("[ERROR] Backup failed!")

            return return_code == 0

        except KeyboardInterrupt:
            print("\n[INTERRUPT] Backup interrupted by user")
            if self.backup_process:
                self.backup_process.terminate()
            return False
        except Exception as e:
            print(f"[ERROR] Error running backup: {e}")
            return False

    def _cleanup_processes(self):
        """Clean up background processes."""
        if self.monitor_process:
            try:
                self.monitor_process.terminate()
                self.monitor_process.wait(timeout=5)
                print("[STOP] Resource monitor stopped")
            except Exception:
                try:
                    self.monitor_process.kill()
                except Exception:
                    pass

        if self.backup_process:
            try:
                self.backup_process.terminate()
                self.backup_process.wait(timeout=10)
                print("[STOP] Backup process stopped")
            except Exception:
                try:
                    self.backup_process.kill()
                except Exception:
                    pass

    def show_usage(self):
        """Show usage instructions."""
        print("""
TNGD Backup System v2.0 - Professional Runner
================================================

The system performs complete data backup for all configured tables.

Usage:
    python run_backup.py [OPTIONS]

Options:
    -h, --help                  Show this help message
    --production                Use production config (all 65 tables, optimized settings)
    --config FILE               Use custom config file path
    --check-only                Only check system readiness
    --no-monitor                Skip resource monitoring

Examples:
    python run_backup.py                                 # Complete backup with default config
    python run_backup.py --production                    # Complete backup with production config
    python run_backup.py --config custom_config.json     # Complete backup with custom config
    python run_backup.py --check-only                    # Only check system readiness

Environment Modes:
    --production                All 65 tables, production-grade settings
    (default)                   Standard configuration (subset of tables)

Note:
    Date parameters are no longer used. The system now performs complete
    backup of all data for the configured tables.

Features:
    ✓ Professional project structure
    ✓ Resource monitoring and alerts
    ✓ Graceful shutdown handling
    ✓ Real-time progress display
    ✓ Comprehensive error handling
    ✓ Environment-specific configurations
    ✓ Optimized memory and thread management
    ✓ Automatic recovery from failures
        """)

    def run(self, args):
        """Main run method."""
        # Parse arguments
        if len(args) > 0 and args[0] in ['-h', '--help', 'help']:
            self.show_usage()
            return 0

        # Check prerequisites
        if not self.check_prerequisites():
            print("\n[ERROR] Prerequisites check failed!")
            print("Please ensure all required files are present.")
            return 1

        # Parse arguments
        config_file = None
        date_args = []
        no_monitor = False
        check_only = False
        production_mode = False
        resume_mode = False

        i = 0
        while i < len(args):
            if args[i] == '--config' and i + 1 < len(args):
                config_file = args[i + 1]
                i += 2
            elif args[i] == '--production':
                production_mode = True
                i += 1
            elif args[i] == '--no-monitor':
                no_monitor = True
                i += 1
            elif args[i] == '--check-only':
                check_only = True
                i += 1
            elif args[i] == '--resume':
                resume_mode = True
                i += 1
            else:
                # Accept additional arguments (for backward compatibility)
                # Note: Date arguments are no longer used but still accepted
                date_args.append(args[i])
                i += 1

        # Build backup_args with environment flags
        backup_args = []
        if production_mode:
            backup_args.append("--production")
            print("PRODUCTION: Mode selected - will process all 65 tables")

        if resume_mode:
            backup_args.append("--resume")
            print("RESUME: Mode selected - will continue from where backup left off")

        # Add any additional arguments (for backward compatibility)
        if date_args:
            print("NOTE: Date arguments are no longer used in complete backup mode")
            backup_args.extend(date_args)

        # Add custom config file if specified
        if config_file:
            if os.path.exists(config_file):
                backup_args = ["--config", config_file] + backup_args
            else:
                print(f"[ERROR] Config file not found: {config_file}")
                return 1

        # Add check-only flag
        if check_only:
            backup_args = ["--check-only"] + backup_args

        try:
            # Start monitoring unless disabled
            if not no_monitor and not check_only:
                self.start_monitoring()

            # Run backup
            success = self.run_backup(backup_args)

            return 0 if success else 1

        finally:
            # Cleanup
            self._cleanup_processes()


def main():
    """Main entry point."""
    runner = BackupRunner()
    return runner.run(sys.argv[1:])


if __name__ == "__main__":
    sys.exit(main())
