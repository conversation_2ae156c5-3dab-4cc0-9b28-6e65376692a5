"""
Utility modules for TNGD Backup System

This package contains supporting utilities and helper functions:
- monitoring: Resource and system monitoring
- error_handler: Standardized error handling utilities
"""

from .monitoring import ResourceMonitor, SystemHealthChecker
from .error_handler import (
    BackupError, ApiError, NetworkError, ConfigurationError, ResourceError, ValidationError,
    ErrorHandler, with_error_handling, safe_execute,
    get_api_error_handler, get_storage_error_handler, get_processing_error_handler
)

__all__ = [
    # Monitoring utilities
    "ResourceMonitor",
    "SystemHealthChecker",

    # Error handling utilities
    "BackupError", "ApiError", "NetworkError", "ConfigurationError", "ResourceError", "ValidationError",
    "ErrorHandler", "with_error_handling", "safe_execute",
    "get_api_error_handler", "get_storage_error_handler", "get_processing_error_handler"
]
