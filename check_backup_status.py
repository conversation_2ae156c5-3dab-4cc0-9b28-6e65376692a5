#!/usr/bin/env python3
"""
Quick script to check if backup processes are running and their status.
"""

import psutil
import time
from datetime import datetime, timedelta

def check_backup_processes():
    """Check for running backup processes."""
    backup_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cpu_percent', 'memory_info']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if any(keyword in cmdline.lower() for keyword in [
                    'tngd_backup', 'run_backup', 'backup_engine', 'devo_client'
                ]):
                    backup_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return backup_processes

def format_runtime(create_time):
    """Format process runtime."""
    runtime_seconds = time.time() - create_time
    runtime_delta = timedelta(seconds=runtime_seconds)
    
    hours = runtime_delta.seconds // 3600
    minutes = (runtime_delta.seconds % 3600) // 60
    
    if runtime_delta.days > 0:
        return f"{runtime_delta.days}d {hours}h {minutes}m"
    elif hours > 0:
        return f"{hours}h {minutes}m"
    else:
        return f"{minutes}m"

def main():
    """Main function."""
    print("=== BACKUP PROCESS STATUS CHECK ===")
    print(f"Check time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    processes = check_backup_processes()
    
    if not processes:
        print("✓ No backup processes currently running")
        print()
        print("You can safely start a new backup with:")
        print("python run_backup.py 2025-04-15 --production")
        return
    
    print(f"Found {len(processes)} backup process(es):")
    print()
    
    for proc in processes:
        runtime = format_runtime(proc['create_time'])
        memory_mb = proc['memory_info'].rss / (1024 * 1024) if proc['memory_info'] else 0
        
        print(f"PID: {proc['pid']}")
        print(f"Name: {proc['name']}")
        print(f"Runtime: {runtime}")
        print(f"Memory: {memory_mb:.1f} MB")
        print(f"CPU: {proc['cpu_percent']:.1f}%")
        print(f"Command: {' '.join(proc['cmdline'])[:80]}...")
        print("-" * 50)
    
    # Check if processes seem stuck
    stuck_threshold = 3 * 3600  # 3 hours
    stuck_processes = [p for p in processes if time.time() - p['create_time'] > stuck_threshold]
    
    if stuck_processes:
        print("⚠️  WARNING: Some processes have been running for over 3 hours")
        print("   This may indicate a stuck backup process.")
        print()
        print("To fix stuck processes, run:")
        print("python fix_stuck_backup.py")
    else:
        print("✓ Processes appear to be running normally")
        print()
        print("To monitor progress, check the latest log file in data/logs/")

if __name__ == "__main__":
    main()
