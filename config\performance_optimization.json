{"version": "1.0", "description": "TNGD Backup System - Performance Optimization Configuration", "performance_tracking": {"enabled": true, "max_history_entries": 1000, "analysis_cache_ttl_seconds": 300, "baseline_sample_size": 10, "enable_system_metrics": true, "log_slow_operations_threshold_seconds": 30, "export_metrics_enabled": true}, "progressive_chunk_sizing": {"enabled": true, "initial_chunk_size": 50000, "min_chunk_size": 5000, "max_chunk_size": 200000, "performance_threshold_seconds": 30.0, "memory_pressure_threshold_percent": 80.0, "size_increase_factor": 1.2, "size_decrease_factor": 0.8, "consecutive_good_chunks_threshold": 3, "consecutive_bad_chunks_threshold": 2}, "adaptive_compression": {"enabled": true, "performance_threshold_seconds": 60.0, "compression_levels": {"fast": 1, "balanced": 3, "maximum": 6}, "size_thresholds": {"small_file_mb": 100, "large_file_mb": 1000}, "compressibility_thresholds": {"high_text_ratio": 0.7, "low_text_ratio": 0.3}}, "smart_throttling": {"enabled": true, "memory_warning_threshold_percent": 75.0, "memory_critical_threshold_percent": 90.0, "cpu_warning_threshold_percent": 80.0, "cpu_critical_threshold_percent": 95.0, "throttle_cooldown_seconds": 30, "adaptive_threshold_adjustment": true, "throttle_actions": {"memory_throttle": {"garbage_collection": true, "pause_seconds": 0.5}, "critical_memory_throttle": {"aggressive_gc_cycles": 3, "pause_seconds": 2.0}, "cpu_throttle": {"pause_seconds": 1.0}, "critical_cpu_throttle": {"pause_seconds": 3.0}}}, "smart_retry": {"enabled": true, "strategies": {"network": {"max_retries": 5, "base_delay_seconds": 2, "backoff_factor": 2.0, "max_delay_seconds": 60, "jitter_percent": 20}, "memory": {"max_retries": 3, "base_delay_seconds": 5, "backoff_factor": 1.5, "max_delay_seconds": 30, "jitter_percent": 20}, "timeout": {"max_retries": 4, "base_delay_seconds": 10, "backoff_factor": 1.8, "max_delay_seconds": 120, "jitter_percent": 20}, "resource": {"max_retries": 3, "base_delay_seconds": 15, "backoff_factor": 2.0, "max_delay_seconds": 180, "jitter_percent": 20}, "validation": {"max_retries": 2, "base_delay_seconds": 1, "backoff_factor": 1.0, "max_delay_seconds": 5, "jitter_percent": 10}, "default": {"max_retries": 3, "base_delay_seconds": 5, "backoff_factor": 2.0, "max_delay_seconds": 60, "jitter_percent": 20}}, "error_classification": {"network_patterns": ["connection", "network", "dns", "socket", "timeout", "unreachable", "connection refused", "connection reset", "connection timeout"], "memory_patterns": ["memory", "out of memory", "memoryerror", "allocation failed"], "timeout_patterns": ["timeout", "timed out", "deadline exceeded"], "resource_patterns": ["resource", "busy", "locked", "unavailable", "throttled", "rate limit", "quota exceeded", "too many requests"], "validation_patterns": ["validation", "invalid", "malformed", "bad request"]}}, "performance_thresholds": {"operation_duration": {"fast_seconds": 10, "normal_seconds": 60, "slow_seconds": 300, "very_slow_seconds": 600}, "throughput": {"excellent_rows_per_second": 10000, "good_rows_per_second": 5000, "acceptable_rows_per_second": 1000, "poor_rows_per_second": 500}, "memory_usage": {"low_mb": 500, "normal_mb": 1000, "high_mb": 2000, "critical_mb": 4000}, "success_rate": {"excellent_percent": 99.0, "good_percent": 95.0, "acceptable_percent": 90.0, "poor_percent": 80.0}}, "optimization_recommendations": {"auto_apply_safe_optimizations": false, "recommendation_categories": {"chunk_sizing": {"enabled": true, "auto_apply": false}, "compression": {"enabled": true, "auto_apply": false}, "memory_management": {"enabled": true, "auto_apply": true}, "retry_strategy": {"enabled": true, "auto_apply": true}, "resource_throttling": {"enabled": true, "auto_apply": true}}, "notification_thresholds": {"performance_degradation_percent": 20, "success_rate_drop_percent": 5, "memory_usage_increase_percent": 50}}, "monitoring_intervals": {"performance_analysis_seconds": 300, "system_health_check_seconds": 30, "metrics_export_seconds": 3600, "recommendation_update_seconds": 600}, "data_retention": {"performance_metrics_days": 7, "analysis_results_days": 30, "recommendation_history_days": 14, "error_statistics_days": 30}, "integration": {"enable_email_reports": true, "enable_dashboard_metrics": true, "enable_log_analysis": true, "enable_external_monitoring": false, "metrics_export_format": "json", "real_time_alerts": {"enabled": true, "alert_channels": ["log", "email"], "critical_performance_degradation": true, "memory_pressure_alerts": true, "high_failure_rate_alerts": true}}}