#!/usr/bin/env python3
"""
Test script for thread pool cleanup improvements.

This script tests the Phase 1 conservative thread management improvements:
- Conservative thread limits
- Proper synchronous shutdown
- Aggressive cleanup intervals
- Windows Server optimization
"""

import sys
import time
import threading
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add the src directory to the path
sys.path.insert(0, 'src')

from tngd_backup.core.thread_manager import get_thread_manager, managed_thread_pool
from tngd_backup.constants import BackupConstants

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def dummy_work(task_id: int, duration: float = 1.0):
    """Simulate some work that takes time."""
    logger.info(f"Task {task_id} starting (duration: {duration}s)")
    time.sleep(duration)
    logger.info(f"Task {task_id} completed")
    return f"Task {task_id} result"


def test_conservative_limits():
    """Test that conservative thread limits are working."""
    logger.info("=== Testing Conservative Thread Limits ===")
    
    # Print current constants
    logger.info(f"MAX_THREADS: {BackupConstants.DEFAULT_MAX_THREADS}")
    logger.info(f"THREAD_POOL_SIZE: {BackupConstants.DEFAULT_THREAD_POOL_SIZE}")
    logger.info(f"THREAD_WARNING: {BackupConstants.THREAD_WARNING_THRESHOLD}")
    logger.info(f"THREAD_CRITICAL: {BackupConstants.THREAD_CRITICAL_THRESHOLD}")
    logger.info(f"CLEANUP_INTERVAL: {BackupConstants.DEFAULT_CLEANUP_INTERVAL_SECONDS}")
    
    # Get initial thread count
    initial_threads = threading.active_count()
    logger.info(f"Initial thread count: {initial_threads}")
    
    return True


def test_thread_pool_cleanup():
    """Test thread pool creation and cleanup."""
    logger.info("=== Testing Thread Pool Cleanup ===")
    
    thread_manager = get_thread_manager()
    
    # Get initial metrics
    initial_metrics = thread_manager.get_thread_metrics()
    logger.info(f"Initial metrics: {initial_metrics}")
    
    # Create multiple thread pools and run tasks
    tasks = []
    pool_names = ['test-pool-1', 'test-pool-2', 'test-pool-3']
    
    for pool_name in pool_names:
        logger.info(f"Creating pool: {pool_name}")
        
        with managed_thread_pool(pool_name, max_workers=2) as pool:
            # Submit some tasks
            for i in range(3):
                task_id = f"{pool_name}-task-{i}"
                future = pool.submit(dummy_work, task_id, 0.5)
                tasks.append(future)
        
        # Check metrics after each pool
        metrics = thread_manager.get_thread_metrics()
        logger.info(f"Metrics after {pool_name}: {metrics}")
    
    # Wait for all tasks to complete
    logger.info("Waiting for all tasks to complete...")
    for future in as_completed(tasks, timeout=30):
        try:
            result = future.result()
            logger.debug(f"Task result: {result}")
        except Exception as e:
            logger.error(f"Task failed: {e}")
    
    # Wait for cleanup
    logger.info("Waiting for automatic cleanup...")
    time.sleep(BackupConstants.DEFAULT_CLEANUP_INTERVAL_SECONDS + 5)
    
    # Get final metrics
    final_metrics = thread_manager.get_thread_metrics()
    logger.info(f"Final metrics: {final_metrics}")
    
    return True


def test_force_cleanup():
    """Test force cleanup functionality."""
    logger.info("=== Testing Force Cleanup ===")
    
    thread_manager = get_thread_manager()
    
    # Create some pools
    with managed_thread_pool('force-test-1', max_workers=2) as pool1:
        with managed_thread_pool('force-test-2', max_workers=2) as pool2:
            # Submit long-running tasks
            futures = []
            for i in range(4):
                future = pool1.submit(dummy_work, f"long-task-{i}", 2.0)
                futures.append(future)
            
            # Check metrics before cleanup
            before_metrics = thread_manager.get_thread_metrics()
            logger.info(f"Before force cleanup: {before_metrics}")
            
            # Force cleanup
            thread_manager._force_cleanup()
            
            # Check metrics after cleanup
            after_metrics = thread_manager.get_thread_metrics()
            logger.info(f"After force cleanup: {after_metrics}")
    
    return True


def test_thread_limit_enforcement():
    """Test that thread limits are properly enforced."""
    logger.info("=== Testing Thread Limit Enforcement ===")
    
    thread_manager = get_thread_manager()
    
    try:
        # Try to create a pool that would exceed limits
        large_pool_size = BackupConstants.THREAD_CRITICAL_THRESHOLD + 10
        logger.info(f"Attempting to create pool with {large_pool_size} workers...")
        
        with managed_thread_pool('large-pool', max_workers=large_pool_size) as pool:
            logger.warning("Large pool created - this should not happen!")
            
    except RuntimeError as e:
        logger.info(f"Thread limit properly enforced: {e}")
        return True
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False
    
    return False


def main():
    """Run all thread cleanup tests."""
    logger.info("Starting Thread Pool Cleanup Tests")
    logger.info("=" * 50)
    
    tests = [
        test_conservative_limits,
        test_thread_pool_cleanup,
        test_force_cleanup,
        test_thread_limit_enforcement
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
            logger.info(f"✓ {test_func.__name__}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            logger.error(f"✗ {test_func.__name__}: ERROR - {e}")
            results.append(False)
        
        # Small delay between tests
        time.sleep(2)
    
    # Summary
    logger.info("=" * 50)
    passed = sum(results)
    total = len(results)
    logger.info(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Thread cleanup improvements are working.")
    else:
        logger.warning(f"⚠️  {total - passed} tests failed. Review the logs above.")
    
    # Final cleanup
    thread_manager = get_thread_manager()
    thread_manager.shutdown()
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
