#!/usr/bin/env python3
"""
Quick script to check recent backup logs for row count verification patterns.
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime

def analyze_backup_logs():
    """Analyze recent backup logs for row count patterns."""
    
    print("🔍 Analyzing Backup Logs for Row Count Patterns")
    print("=" * 60)
    
    # Look for log files
    log_patterns = [
        "logs/*.log",
        "logs/backup_*.log", 
        "*.log",
        "backup_*.log"
    ]
    
    log_files = []
    for pattern in log_patterns:
        log_files.extend(Path(".").glob(pattern))
    
    if not log_files:
        print("❌ No log files found. Please run this script from the TNGD directory.")
        return
    
    print(f"📄 Found {len(log_files)} log files")
    
    # Patterns to look for
    patterns = {
        'chunk_size_50k': re.compile(r'50,000 rows|50000 rows'),
        'row_verification': re.compile(r'ROW_VERIFICATION.*?(\d{1,3}(?:,\d{3})*)\s+rows'),
        'data_retrieved': re.compile(r'DATA.*?(\d{1,3}(?:,\d{3})*)\s+rows'),
        'query_completed': re.compile(r'Query completed.*?(\d{1,3}(?:,\d{3})*)\s+rows'),
        'table_completed': re.compile(r'Table completed.*?(\d{1,3}(?:,\d{3})*)\s+rows'),
        'chunk_saved': re.compile(r'CHUNK.*?(\d{1,3}(?:,\d{3})*)\s+rows')
    }
    
    results = {
        'files_analyzed': 0,
        'total_lines': 0,
        'patterns_found': {pattern: [] for pattern in patterns.keys()},
        'suspicious_50k_counts': [],
        'row_count_summary': {}
    }
    
    for log_file in sorted(log_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]:  # Last 5 files
        print(f"\n📖 Analyzing: {log_file}")
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                results['files_analyzed'] += 1
                results['total_lines'] += len(lines)
                
                for line_num, line in enumerate(lines, 1):
                    # Check for 50,000 row patterns (the issue)
                    if '50,000' in line or '50000' in line:
                        results['suspicious_50k_counts'].append({
                            'file': str(log_file),
                            'line': line_num,
                            'content': line.strip()
                        })
                    
                    # Check other patterns
                    for pattern_name, pattern in patterns.items():
                        matches = pattern.findall(line)
                        if matches:
                            for match in matches:
                                results['patterns_found'][pattern_name].append({
                                    'file': str(log_file),
                                    'line': line_num,
                                    'content': line.strip(),
                                    'row_count': match
                                })
        
        except Exception as e:
            print(f"   ❌ Error reading {log_file}: {e}")
            continue
    
    # Analysis
    print(f"\n📊 ANALYSIS RESULTS")
    print("=" * 60)
    print(f"Files analyzed: {results['files_analyzed']}")
    print(f"Total lines processed: {results['total_lines']:,}")
    
    # Check for 50,000 row issue
    if results['suspicious_50k_counts']:
        print(f"\n🚨 FOUND {len(results['suspicious_50k_counts'])} instances of exactly 50,000 rows:")
        for item in results['suspicious_50k_counts'][:10]:  # Show first 10
            print(f"   📄 {Path(item['file']).name}:{item['line']} - {item['content'][:100]}...")
        
        if len(results['suspicious_50k_counts']) > 10:
            print(f"   ... and {len(results['suspicious_50k_counts']) - 10} more")
    
    # Pattern summary
    print(f"\n📈 PATTERN SUMMARY:")
    for pattern_name, matches in results['patterns_found'].items():
        if matches:
            print(f"   {pattern_name}: {len(matches)} matches")
            
            # Show unique row counts
            row_counts = [match['row_count'] for match in matches]
            unique_counts = list(set(row_counts))
            if len(unique_counts) <= 10:
                print(f"      Row counts found: {', '.join(unique_counts)}")
            else:
                print(f"      {len(unique_counts)} unique row counts found")
    
    # Save results
    results_file = f"backup_log_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if results['suspicious_50k_counts']:
        print("   🔧 The 50,000 row issue is confirmed in your logs")
        print("   🔧 Run the updated backup system to fix this issue")
        print("   🔧 Check email reports after the next backup run")
    else:
        print("   ✅ No obvious 50,000 row issues found in recent logs")
    
    return results

if __name__ == "__main__":
    analyze_backup_logs()
