#!/usr/bin/env python3
"""
Resume backup script that skips already completed tables and continues from where it left off.
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

def setup_logging():
    """Setup logging for the resume script."""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file = f"resume_backup_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file)
        ]
    )
    return logging.getLogger(__name__)

def load_tables_list():
    """Load the production tables list."""
    tables_file = Path("config/tables_production.json")
    if not tables_file.exists():
        tables_file = Path("config/tables.json")
    
    if not tables_file.exists():
        raise FileNotFoundError("No tables configuration file found")
    
    with open(tables_file, 'r') as f:
        tables = json.load(f)
    
    return tables

def find_completed_tables(date_str="2025-04-15"):
    """Find tables that have already been successfully backed up."""
    completed_tables = set()
    
    # Check OSS storage for completed uploads (this would require OSS client)
    # For now, we'll check local export directories and log files
    
    # Check export directories
    export_dirs = list(Path("data/exports").glob("*"))
    for export_dir in export_dirs:
        if export_dir.is_dir():
            # Look for tar.gz files that indicate completed backups
            for archive in export_dir.glob("*.tar.gz"):
                # Extract table name from filename
                filename = archive.stem.replace(".tar", "")
                if filename.endswith(f"_{date_str}"):
                    table_name = filename.replace(f"_{date_str}", "").replace("_", ".")
                    completed_tables.add(table_name)
    
    # Check log files for completion messages
    log_dir = Path("data/logs")
    if log_dir.exists():
        for log_file in log_dir.glob("*.log"):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Look for completion messages
                    lines = content.split('\n')
                    for line in lines:
                        if "[COMPLETE] Table completed:" in line and date_str in content:
                            # Extract table name from log line
                            try:
                                table_part = line.split("[COMPLETE] Table completed:")[1].strip()
                                table_name = table_part.split("(")[0].strip()
                                completed_tables.add(table_name)
                            except (IndexError, AttributeError):
                                continue
            except Exception:
                continue
    
    return completed_tables

def find_last_processed_table():
    """Find the last table that was being processed when the backup stopped."""
    log_dir = Path("data/logs")
    if not log_dir.exists():
        return None
    
    # Find the most recent log file
    log_files = list(log_dir.glob("*.log"))
    if not log_files:
        return None
    
    latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
    
    try:
        with open(latest_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Look for the last "[PROCESS] Processing table" message
        last_table = None
        for line in reversed(lines):
            if "[PROCESS] Processing table" in line:
                try:
                    # Extract table name from log line
                    # Format: [PROCESS] Processing table X/Y: table.name
                    table_part = line.split(": ")[1].strip()
                    last_table = table_part
                    break
                except (IndexError, AttributeError):
                    continue
        
        return last_table
        
    except Exception as e:
        print(f"Error reading log file: {e}")
        return None

def create_resume_tables_list(all_tables, completed_tables, last_processed_table=None):
    """Create a list of tables to process, excluding completed ones."""
    remaining_tables = []
    
    # If we know the last processed table, start from there
    if last_processed_table and last_processed_table in all_tables:
        start_index = all_tables.index(last_processed_table)
        # Include the last processed table in case it didn't complete
        remaining_tables = all_tables[start_index:]
    else:
        remaining_tables = all_tables.copy()
    
    # Remove completed tables
    remaining_tables = [table for table in remaining_tables if table not in completed_tables]
    
    return remaining_tables

def main():
    """Main function to resume backup."""
    logger = setup_logging()
    logger.info("=== BACKUP RESUME SCRIPT ===")
    
    try:
        # Load tables list
        all_tables = load_tables_list()
        logger.info(f"Loaded {len(all_tables)} tables from configuration")
        
        # Find completed tables
        completed_tables = find_completed_tables()
        logger.info(f"Found {len(completed_tables)} completed tables:")
        for table in sorted(completed_tables):
            logger.info(f"  ✓ {table}")
        
        # Find last processed table
        last_processed = find_last_processed_table()
        if last_processed:
            logger.info(f"Last processed table: {last_processed}")
        
        # Create resume list
        remaining_tables = create_resume_tables_list(all_tables, completed_tables, last_processed)
        
        if not remaining_tables:
            logger.info("All tables appear to be completed!")
            return
        
        logger.info(f"Tables remaining to process: {len(remaining_tables)}")
        logger.info("Remaining tables:")
        for i, table in enumerate(remaining_tables, 1):
            logger.info(f"  {i:2d}. {table}")
        
        # Create a custom tables file for resume
        resume_tables_file = Path("config/tables_resume.json")
        with open(resume_tables_file, 'w') as f:
            json.dump(remaining_tables, f, indent=2)
        
        logger.info(f"Created resume tables file: {resume_tables_file}")
        
        # Show command to run
        logger.info("\n" + "="*60)
        logger.info("To resume the backup, run:")
        logger.info("python run_backup.py 2025-04-15 --production --resume")
        logger.info("="*60)
        
        # Ask if user wants to start immediately
        response = input("\nDo you want to start the resume backup now? (y/N): ").strip().lower()
        if response == 'y':
            logger.info("Starting resume backup...")
            os.system("python run_backup.py 2025-04-15 --production --resume")
        
    except Exception as e:
        logger.error(f"Error during resume preparation: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
