# TNGD Backup System - Optimization API Reference

## 🔧 Core Classes and Methods

### PerformanceOptimizer

Main orchestrator for all optimization features.

```python
from tngd_backup.core.performance_optimizer import PerformanceOptimizer

optimizer = PerformanceOptimizer(config_path="config/performance_optimization.json")
```

#### Methods

##### `optimize_chunk_size(operation: str, current_size: int, performance_data: Dict) -> int`
Optimizes chunk size based on performance history.

**Parameters:**
- `operation`: Name of the operation
- `current_size`: Current chunk size
- `performance_data`: Dictionary with performance metrics

**Returns:** Optimized chunk size

**Example:**
```python
performance_data = {
    'avg_duration': 30.0,
    'success_rate': 0.95,
    'memory_pressure': 60.0
}
new_size = optimizer.optimize_chunk_size("backup_table_users", 50000, performance_data)
```

##### `optimize_compression(data_characteristics: Dict) -> Dict`
Selects optimal compression settings.

**Parameters:**
- `data_characteristics`: Data size, type, and compressibility info

**Returns:** Dictionary with compression settings

**Example:**
```python
data_chars = {
    'size_mb': 200,
    'compressibility': 'high',
    'data_type': 'text'
}
compression_settings = optimizer.optimize_compression(data_chars)
```

##### `should_apply_throttling() -> Tuple[bool, Dict]`
Determines if throttling should be applied.

**Returns:** Tuple of (should_throttle, throttle_actions)

##### `record_operation_performance(operation: str, duration: float, throughput: float, success: bool, metadata: Dict = None)`
Records performance metrics for an operation.

### PerformanceTracker

Tracks and analyzes performance metrics.

```python
from tngd_backup.utils.performance_tracker import PerformanceTracker

tracker = PerformanceTracker(max_history=1000)
```

#### Methods

##### `record_operation(operation: str, duration: float, throughput: float, success: bool, metadata: Dict = None)`
Records performance data for analysis.

##### `analyze_performance(operation: str = None) -> Dict`
Analyzes performance and generates recommendations.

**Returns:** Dictionary with performance analysis

##### `get_system_recommendations() -> List[str]`
Gets system-wide optimization recommendations.

### ResourceMonitor

Monitors system resources and applies throttling.

```python
from tngd_backup.utils.monitoring import ResourceMonitor

monitor = ResourceMonitor()
monitor.start_monitoring()
```

#### Methods

##### `get_system_health() -> SystemHealth`
Gets current system health metrics.

##### `apply_throttling(operation_context: str)`
Applies smart throttling based on system conditions.

##### `get_throttling_status() -> Dict`
Gets current throttling status and statistics.

### ErrorHandler

Provides intelligent retry logic and error classification.

```python
from tngd_backup.utils.error_handler import ErrorHandler, smart_retry

error_handler = ErrorHandler()
```

#### Methods

##### `classify_error(error: Exception) -> str`
Classifies error type for appropriate retry strategy.

**Returns:** Error category ('network', 'memory', 'timeout', etc.)

##### `should_retry(error: Exception, attempt: int, operation: str) -> Tuple[bool, float]`
Determines if operation should be retried.

**Returns:** Tuple of (should_retry, delay_seconds)

##### `get_retry_strategy(error: Exception, operation: str) -> Dict`
Gets optimized retry strategy for the error.

#### Decorators

##### `@smart_retry(operation_name: str)`
Decorator for automatic smart retry logic.

**Example:**
```python
@smart_retry("database_query")
def query_database():
    # Your database query code
    pass
```

### CompressionService

Provides adaptive compression capabilities.

```python
from tngd_backup.core.compression_service import CompressionService

compression = CompressionService()
```

#### Methods

##### `adaptive_compress(source_path: str, output_path: str, data_characteristics: Dict = None) -> Tuple[bool, str, Dict]`
Compresses using adaptive algorithm selection.

**Returns:** Tuple of (success, output_file_path, compression_stats)

##### `get_compression_recommendations() -> List[str]`
Gets compression optimization recommendations.

### ChunkManager

Manages progressive chunk sizing.

```python
from tngd_backup.core.streaming_processor import ChunkManager, StreamingConfig

config = StreamingConfig()
config.enable_progressive_sizing = True
chunk_manager = ChunkManager(config)
```

#### Methods

##### `calculate_chunks(total_rows: int, table_name: str = None) -> List[ChunkInfo]`
Calculates optimal chunks with progressive sizing.

##### `record_chunk_performance(chunk_id: int, processing_time: float, memory_pressure: float, success: bool)`
Records chunk performance for optimization.

## 🔧 Configuration Classes

### StreamingConfig

Configuration for streaming and chunk processing.

```python
@dataclass
class StreamingConfig:
    # Chunk size settings
    default_chunk_size: int = 100000
    max_chunk_size: int = 500000
    min_chunk_size: int = 10000
    
    # Progressive sizing
    enable_progressive_sizing: bool = True
    progressive_size_factor: float = 1.2
    performance_threshold_seconds: float = 30.0
    memory_pressure_threshold: float = 80.0
    
    # Smart retry
    enable_smart_retry: bool = True
    retry_chunk_size_factor: float = 0.5
    max_chunk_retries: int = 3
```

## 📊 Data Models

### PerformanceMetric

Individual performance data point.

```python
@dataclass
class PerformanceMetric:
    timestamp: float
    operation: str
    duration: float
    throughput: float
    memory_usage_mb: float
    cpu_percent: float
    success: bool
    metadata: Dict[str, Any]
```

### PerformanceAnalysis

Performance analysis results.

```python
@dataclass
class PerformanceAnalysis:
    operation: str
    total_operations: int
    success_rate: float
    avg_duration: float
    avg_throughput: float
    avg_memory_usage: float
    avg_cpu_usage: float
    bottlenecks: List[str]
    recommendations: List[str]
    trend: str  # 'improving', 'degrading', 'stable'
```

### SystemHealth

System health snapshot.

```python
@dataclass
class SystemHealth:
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_percent: float
    thread_count: int
    process_count: int
    network_connections: int
    status: str  # 'healthy', 'warning', 'critical'
```

## 🎯 Integration Examples

### Basic Integration

```python
from tngd_backup.core.performance_optimizer import PerformanceOptimizer

def optimized_backup_operation(table_name: str):
    optimizer = PerformanceOptimizer()
    
    # Prepare operation context
    context = {
        'operation': f'backup_table_{table_name}',
        'table_name': table_name,
        'chunk_size': 50000
    }
    
    # Apply optimizations
    optimizations = optimizer.apply_optimizations(context)
    
    # Execute backup with optimizations
    start_time = time.time()
    try:
        # Your backup logic here
        result = perform_backup(table_name, optimizations)
        success = True
    except Exception as e:
        result = None
        success = False
    
    # Record performance
    duration = time.time() - start_time
    optimizer.record_operation_performance(
        context['operation'], duration, 
        result.get('rows_processed', 0) / duration if result else 0,
        success
    )
    
    return result
```

### Advanced Integration with Custom Metrics

```python
class OptimizedBackupEngine:
    def __init__(self):
        self.optimizer = PerformanceOptimizer()
        self.tracker = PerformanceTracker()
        
    def backup_with_monitoring(self, table_name: str):
        # Get historical performance
        analysis = self.tracker.analyze_performance(f"backup_{table_name}")
        
        # Optimize based on history
        if analysis and table_name in analysis:
            table_analysis = analysis[table_name]
            
            # Adjust chunk size based on trend
            if table_analysis.trend == "degrading":
                chunk_size = 25000  # Smaller chunks for degrading performance
            elif table_analysis.trend == "improving":
                chunk_size = 100000  # Larger chunks for improving performance
            else:
                chunk_size = 50000  # Default
        else:
            chunk_size = 50000
        
        # Execute with monitoring
        return self._execute_monitored_backup(table_name, chunk_size)
```

## 🚨 Error Handling

### Custom Error Types

```python
from tngd_backup.utils.error_handler import BackupError, ApiError, ValidationError

# Custom error handling
try:
    result = backup_operation()
except BackupError as e:
    # Handle backup-specific errors
    logger.error(f"Backup error: {e}")
    if e.error_code == "MEMORY_PRESSURE":
        # Apply memory optimization
        pass
```

### Retry Strategies

```python
# Custom retry configuration
retry_strategies = {
    'network': {
        'max_retries': 5,
        'base_delay_seconds': 2,
        'backoff_factor': 2.0,
        'max_delay_seconds': 60
    },
    'memory': {
        'max_retries': 3,
        'base_delay_seconds': 5,
        'backoff_factor': 1.5,
        'max_delay_seconds': 30
    }
}

error_handler = ErrorHandler()
error_handler.retry_strategies.update(retry_strategies)
```

## 📈 Performance Monitoring

### Custom Metrics Collection

```python
# Collect custom performance metrics
def collect_custom_metrics():
    tracker = PerformanceTracker()
    
    # Record custom operation
    tracker.record_operation(
        "custom_data_processing",
        duration=45.2,
        throughput=2500,
        success=True,
        metadata={
            'data_source': 'api',
            'processing_type': 'transformation',
            'custom_metric': 123.45
        }
    )
    
    # Export metrics for external analysis
    metrics = tracker.export_metrics(
        operation="custom_data_processing",
        start_time=time.time() - 3600  # Last hour
    )
    
    return metrics
```

### Real-time Monitoring

```python
# Set up real-time monitoring
def setup_monitoring():
    monitor = ResourceMonitor()
    monitor.start_monitoring(interval=30)  # Check every 30 seconds
    
    # Custom threshold handling
    def check_custom_thresholds():
        health = monitor.get_system_health()
        if health.memory_percent > 90:
            # Custom high memory handling
            trigger_memory_cleanup()
    
    return monitor
```

---

*This API reference provides detailed information for developers integrating with the TNGD Backup System optimization features.*
