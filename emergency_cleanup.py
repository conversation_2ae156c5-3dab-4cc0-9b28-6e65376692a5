#!/usr/bin/env python3
"""
Emergency Resource Cleanup Script

This script helps diagnose and fix critical resource issues in the TNGD backup system.
Use this when the system is experiencing high thread counts, memory pressure, or hanging queries.
"""

import os
import sys
import time
import threading
import gc
import psutil
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import constants after adding src to path
from tngd_backup.constants import BackupConstants

def setup_logging():
    """Setup logging for the emergency cleanup script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('emergency_cleanup.log')
        ]
    )
    return logging.getLogger(__name__)

def get_system_status():
    """Get current system resource status."""
    try:
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Thread count
        thread_count = threading.active_count()
        
        # Process info
        process = psutil.Process()
        process_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / 1024 / 1024 / 1024,
            'thread_count': thread_count,
            'process_memory_mb': process_memory
        }
    except Exception as e:
        return {'error': str(e)}

def emergency_thread_cleanup(logger):
    """Perform emergency thread cleanup."""
    logger.info("Starting emergency thread cleanup...")
    
    try:
        # Get all threads
        all_threads = threading.enumerate()
        logger.info(f"Total threads before cleanup: {len(all_threads)}")
        
        # Identify daemon threads
        daemon_threads = [t for t in all_threads if t.daemon and t != threading.current_thread()]
        logger.info(f"Daemon threads found: {len(daemon_threads)}")
        
        # Log thread names for debugging
        thread_names = {}
        for thread in daemon_threads:
            name_prefix = thread.name.split('-')[0] if '-' in thread.name else thread.name
            thread_names[name_prefix] = thread_names.get(name_prefix, 0) + 1
        
        logger.info(f"Thread types: {thread_names}")
        
        # Try to cleanup thread pools if available
        try:
            from tngd_backup.core.thread_manager import get_thread_manager
            thread_manager = get_thread_manager()
            thread_manager.emergency_shutdown()
            logger.info("Thread manager emergency shutdown completed")
        except Exception as e:
            logger.warning(f"Thread manager cleanup failed: {e}")
        
        # Force garbage collection
        for i in range(5):
            collected = gc.collect()
            logger.info(f"Garbage collection round {i+1}: {collected} objects collected")
            time.sleep(0.5)
        
        # Check final thread count
        final_threads = threading.active_count()
        logger.info(f"Thread count after cleanup: {final_threads}")
        
        return final_threads
        
    except Exception as e:
        logger.error(f"Emergency thread cleanup failed: {e}")
        return None

def kill_hanging_processes(logger, force_terminate=False):
    """Kill any hanging backup processes.

    Args:
        logger: Logger instance for output
        force_terminate: If True, actually terminate high-resource processes
    """
    logger.info("Checking for hanging backup processes...")

    try:
        current_process = psutil.Process()

        # Look for child processes
        children = current_process.children(recursive=True)
        logger.info(f"Found {len(children)} child processes")

        terminated_count = 0

        for child in children:
            try:
                # Check if process is consuming resources
                cpu_percent = child.cpu_percent()
                memory_mb = child.memory_info().rss / 1024 / 1024

                logger.info(f"Child process {child.pid}: CPU={cpu_percent:.1f}%, Memory={memory_mb:.1f}MB")

                # If process is using significant resources, terminate if requested
                if cpu_percent > BackupConstants.PROCESS_CPU_THRESHOLD or memory_mb > BackupConstants.PROCESS_MEMORY_THRESHOLD_MB:
                    logger.warning(f"High resource usage detected in process {child.pid}")

                    if force_terminate:
                        try:
                            logger.warning(f"Terminating high-resource process {child.pid}")
                            child.terminate()
                            terminated_count += 1

                            # Wait a moment for graceful termination
                            try:
                                child.wait(timeout=3)
                                logger.info(f"Process {child.pid} terminated gracefully")
                            except psutil.TimeoutExpired:
                                logger.warning(f"Process {child.pid} did not terminate gracefully, forcing kill")
                                child.kill()

                        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                            logger.warning(f"Could not terminate process {child.pid}: {e}")
                    else:
                        logger.info(f"Would terminate process {child.pid} (use --force to actually terminate)")

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if terminated_count > 0:
            logger.info(f"Terminated {terminated_count} high-resource processes")
        elif force_terminate:
            logger.info("No processes needed termination")

    except Exception as e:
        logger.error(f"Process cleanup failed: {e}")

def main():
    """Main emergency cleanup function."""
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Emergency resource cleanup for TNGD backup system")
    parser.add_argument("--force", action="store_true", help="Force termination of high-resource processes")
    parser.add_argument("--check-only", action="store_true", help="Only check system status, don't perform cleanup")
    args = parser.parse_args()

    logger = setup_logging()
    logger.info("=== EMERGENCY RESOURCE CLEANUP STARTED ===")

    # Get initial system status
    initial_status = get_system_status()
    logger.info(f"Initial system status: {initial_status}")

    # Check if emergency action is needed
    if 'error' in initial_status:
        logger.error(f"Could not get system status: {initial_status['error']}")
        return

    needs_cleanup = (
        initial_status.get('thread_count', 0) > BackupConstants.THREAD_WARNING_THRESHOLD or
        initial_status.get('memory_percent', 0) > BackupConstants.MEMORY_CRITICAL_THRESHOLD or
        initial_status.get('cpu_percent', 0) > BackupConstants.CPU_CRITICAL_THRESHOLD
    )

    if args.check_only:
        if needs_cleanup:
            logger.warning("System needs cleanup (thread count, memory, or CPU usage too high)")
        else:
            logger.info("System appears healthy, no cleanup needed")
        return

    if not needs_cleanup:
        logger.info("System appears healthy, no emergency cleanup needed")
        return

    logger.warning("Emergency conditions detected, starting cleanup...")

    # Perform emergency cleanup
    emergency_thread_cleanup(logger)

    # Check for hanging processes
    kill_hanging_processes(logger, force_terminate=args.force)

    # Wait a moment for cleanup to take effect
    time.sleep(5)

    # Get final system status
    final_status = get_system_status()
    logger.info(f"Final system status: {final_status}")

    # Calculate improvements
    if 'error' not in final_status:
        thread_reduction = initial_status.get('thread_count', 0) - final_status.get('thread_count', 0)
        memory_reduction = initial_status.get('memory_percent', 0) - final_status.get('memory_percent', 0)

        logger.info(f"Cleanup results:")
        logger.info(f"  Thread reduction: {thread_reduction}")
        logger.info(f"  Memory reduction: {memory_reduction:.1f}%")

    logger.info("=== EMERGENCY CLEANUP COMPLETED ===")

if __name__ == "__main__":
    main()
