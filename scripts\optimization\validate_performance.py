#!/usr/bin/env python3
"""
Performance Validation Script

This script validates the performance optimizations by running controlled tests
with different data scenarios and measuring the improvements.
"""

import os
import sys
import time
import json
import logging
import tempfile
import statistics
from pathlib import Path
from typing import Dict, List, Any

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from tngd_backup.core.performance_optimizer import PerformanceOptimizer
from tngd_backup.utils.performance_tracker import PerformanceTracker
from tngd_backup.core.compression_service import CompressionService
from tngd_backup.core.streaming_processor import Chunk<PERSON>anager, StreamingConfig


class PerformanceValidator:
    """Validates performance optimization features with controlled tests."""
    
    def __init__(self):
        """Initialize the performance validator."""
        self.logger = logging.getLogger(__name__)
        self.test_dir = Path(tempfile.mkdtemp())
        self.results = {}
        
        # Initialize components
        self.performance_optimizer = PerformanceOptimizer()
        self.performance_tracker = PerformanceTracker()
        self.compression_service = CompressionService()
        
        self.logger.info(f"Performance validation initialized, test dir: {self.test_dir}")
    
    def cleanup(self):
        """Clean up test resources."""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def create_test_data(self, size_mb: int, data_type: str = "json") -> Path:
        """Create test data files of specified size and type."""
        test_file = self.test_dir / f"test_data_{size_mb}mb_{data_type}.txt"
        
        if data_type == "json":
            # Create JSON-like data (highly compressible)
            sample_record = '{"id": %d, "timestamp": "2024-01-01T%02d:%02d:%02d", "data": "sample_data_%d", "value": %d}\n'
            record_size = len(sample_record % (1, 1, 1, 1, 1, 1))
            records_needed = (size_mb * 1024 * 1024) // record_size
            
            with open(test_file, 'w') as f:
                for i in range(records_needed):
                    f.write(sample_record % (i, i % 24, (i * 2) % 60, (i * 3) % 60, i, i % 1000))
        
        elif data_type == "binary":
            # Create binary-like data (less compressible)
            import random
            with open(test_file, 'wb') as f:
                for _ in range(size_mb * 1024):
                    chunk = bytes([random.randint(0, 255) for _ in range(1024)])
                    f.write(chunk)
        
        else:  # text
            # Create text data (medium compressibility)
            words = ["the", "quick", "brown", "fox", "jumps", "over", "lazy", "dog", "and", "runs"]
            with open(test_file, 'w') as f:
                for i in range(size_mb * 1024 * 100):  # Approximate
                    line = " ".join([words[j % len(words)] for j in range(i % 10 + 5)]) + "\n"
                    f.write(line)
        
        actual_size = test_file.stat().st_size / (1024 * 1024)
        self.logger.info(f"Created test file: {test_file.name} ({actual_size:.1f}MB, {data_type})")
        return test_file
    
    def test_compression_optimization(self) -> Dict[str, Any]:
        """Test adaptive compression with different data types and sizes."""
        print("\n🧪 Testing Compression Optimization...")
        
        test_scenarios = [
            {"size_mb": 10, "data_type": "json", "expected_compressibility": "high"},
            {"size_mb": 50, "data_type": "text", "expected_compressibility": "medium"},
            {"size_mb": 100, "data_type": "json", "expected_compressibility": "high"},
            {"size_mb": 5, "data_type": "binary", "expected_compressibility": "low"}
        ]
        
        compression_results = []
        
        for scenario in test_scenarios:
            print(f"  Testing {scenario['size_mb']}MB {scenario['data_type']} data...")
            
            # Create test data
            test_file = self.create_test_data(scenario['size_mb'], scenario['data_type'])
            
            # Test standard compression
            start_time = time.time()
            success_std, output_std, stats_std = self.compression_service.compress_file(
                str(test_file), str(self.test_dir / f"standard_{test_file.name}.tar.gz")
            )
            std_duration = time.time() - start_time
            
            # Test adaptive compression
            start_time = time.time()
            success_adaptive, output_adaptive, stats_adaptive = self.compression_service.adaptive_compress(
                str(test_file), str(self.test_dir / f"adaptive_{test_file.name}.tar.gz")
            )
            adaptive_duration = time.time() - start_time
            
            # Compare results
            result = {
                "scenario": scenario,
                "standard": {
                    "success": success_std,
                    "duration": std_duration,
                    "compression_ratio": stats_std.get('compression_ratio', 0),
                    "compressed_size": stats_std.get('compressed_size', 0)
                },
                "adaptive": {
                    "success": success_adaptive,
                    "duration": adaptive_duration,
                    "compression_ratio": stats_adaptive.get('compression_ratio', 0),
                    "compressed_size": stats_adaptive.get('compressed_size', 0)
                }
            }
            
            # Calculate improvement
            if success_std and success_adaptive:
                ratio_improvement = (stats_adaptive.get('compression_ratio', 0) - 
                                   stats_std.get('compression_ratio', 0))
                time_improvement = std_duration - adaptive_duration
                
                result["improvements"] = {
                    "compression_ratio": ratio_improvement,
                    "time_saved": time_improvement
                }
                
                print(f"    Compression ratio: {stats_adaptive.get('compression_ratio', 0):.2%} "
                      f"(vs {stats_std.get('compression_ratio', 0):.2%})")
                print(f"    Time: {adaptive_duration:.1f}s (vs {std_duration:.1f}s)")
            
            compression_results.append(result)
        
        print("✅ Compression optimization tests completed")
        return {"compression_tests": compression_results}
    
    def test_chunk_size_optimization(self) -> Dict[str, Any]:
        """Test progressive chunk sizing optimization."""
        print("\n🧪 Testing Chunk Size Optimization...")
        
        config = StreamingConfig()
        config.enable_progressive_sizing = True
        config.default_chunk_size = 10000
        config.min_chunk_size = 1000
        config.max_chunk_size = 100000
        
        chunk_manager = ChunkManager(config)
        
        # Test scenarios with different performance patterns
        scenarios = [
            {"name": "good_performance", "avg_time": 5.0, "memory_pressure": 40.0, "success_rate": 0.98},
            {"name": "slow_performance", "avg_time": 45.0, "memory_pressure": 60.0, "success_rate": 0.85},
            {"name": "memory_pressure", "avg_time": 15.0, "memory_pressure": 90.0, "success_rate": 0.75},
            {"name": "mixed_performance", "avg_time": 25.0, "memory_pressure": 70.0, "success_rate": 0.92}
        ]
        
        chunk_results = []
        
        for scenario in scenarios:
            print(f"  Testing {scenario['name']} scenario...")
            
            # Simulate performance history
            for i in range(10):
                chunk_manager.record_chunk_performance(
                    i + 1, 
                    scenario['avg_time'] + (i % 3 - 1) * 2,  # Add some variation
                    scenario['memory_pressure'] + (i % 5 - 2) * 5,
                    scenario['success_rate'] > 0.8 or i % 4 != 0  # Simulate some failures
                )
            
            # Test progressive sizing
            initial_size = config.default_chunk_size
            optimized_size = chunk_manager._get_progressive_chunk_size(100000, f"test_table_{scenario['name']}")
            
            size_change = (optimized_size - initial_size) / initial_size * 100
            
            result = {
                "scenario": scenario['name'],
                "initial_chunk_size": initial_size,
                "optimized_chunk_size": optimized_size,
                "size_change_percent": size_change,
                "performance_metrics": scenario
            }
            
            print(f"    Chunk size: {initial_size} → {optimized_size} ({size_change:+.1f}%)")
            
            chunk_results.append(result)
        
        print("✅ Chunk size optimization tests completed")
        return {"chunk_size_tests": chunk_results}
    
    def test_performance_tracking_accuracy(self) -> Dict[str, Any]:
        """Test the accuracy of performance tracking and analysis."""
        print("\n🧪 Testing Performance Tracking Accuracy...")
        
        # Create controlled performance scenarios
        scenarios = [
            {"operation": "fast_operation", "durations": [1.0, 1.2, 0.8, 1.1, 0.9], "throughputs": [5000, 4800, 5200, 4900, 5100]},
            {"operation": "slow_operation", "durations": [30.0, 35.0, 28.0, 32.0, 31.0], "throughputs": [500, 450, 550, 480, 520]},
            {"operation": "variable_operation", "durations": [5.0, 15.0, 8.0, 25.0, 12.0], "throughputs": [2000, 800, 1500, 600, 1200]}
        ]
        
        tracking_results = []
        
        for scenario in scenarios:
            print(f"  Testing {scenario['operation']}...")
            
            # Record performance data
            for i, (duration, throughput) in enumerate(zip(scenario['durations'], scenario['throughputs'])):
                success = duration < 20.0  # Simulate failures for very slow operations
                self.performance_tracker.record_operation(
                    scenario['operation'], duration, throughput, success
                )
            
            # Analyze performance
            analysis = self.performance_tracker.analyze_performance(scenario['operation'])
            
            if scenario['operation'] in analysis:
                op_analysis = analysis[scenario['operation']]
                
                # Calculate expected values
                expected_avg_duration = statistics.mean(scenario['durations'])
                expected_avg_throughput = statistics.mean(scenario['throughputs'])
                
                # Compare with tracked values
                duration_accuracy = abs(op_analysis.avg_duration - expected_avg_duration) / expected_avg_duration
                throughput_accuracy = abs(op_analysis.avg_throughput - expected_avg_throughput) / expected_avg_throughput
                
                result = {
                    "operation": scenario['operation'],
                    "expected_avg_duration": expected_avg_duration,
                    "tracked_avg_duration": op_analysis.avg_duration,
                    "duration_accuracy": 1 - duration_accuracy,
                    "expected_avg_throughput": expected_avg_throughput,
                    "tracked_avg_throughput": op_analysis.avg_throughput,
                    "throughput_accuracy": 1 - throughput_accuracy,
                    "recommendations_count": len(op_analysis.recommendations),
                    "trend": op_analysis.trend
                }
                
                print(f"    Duration accuracy: {result['duration_accuracy']:.1%}")
                print(f"    Throughput accuracy: {result['throughput_accuracy']:.1%}")
                print(f"    Trend: {op_analysis.trend}")
                
                tracking_results.append(result)
        
        print("✅ Performance tracking accuracy tests completed")
        return {"tracking_tests": tracking_results}
    
    def test_end_to_end_optimization(self) -> Dict[str, Any]:
        """Test complete end-to-end optimization workflow."""
        print("\n🧪 Testing End-to-End Optimization...")
        
        # Simulate a complete backup operation with optimizations
        operation_context = {
            'operation': 'backup_table_validation',
            'table_name': 'validation_table',
            'chunk_size': 25000,
            'performance_data': {
                'avg_duration': 30.0,
                'success_rate': 0.90,
                'memory_pressure': 75.0
            },
            'data_characteristics': {
                'size_mb': 150,
                'compressibility': 'high',
                'data_type': 'text'
            }
        }
        
        # Apply optimizations
        start_time = time.time()
        optimizations = self.performance_optimizer.apply_optimizations(operation_context)
        optimization_time = time.time() - start_time
        
        # Record performance
        self.performance_optimizer.record_operation_performance(
            "backup_table_validation", 28.5, 2800, True, 
            {'optimizations_applied': optimizations}
        )
        
        # Get comprehensive analysis
        analysis = self.performance_optimizer.get_performance_analysis()
        
        result = {
            "optimization_time": optimization_time,
            "optimizations_applied": optimizations,
            "analysis_timestamp": analysis.get('timestamp'),
            "system_recommendations": analysis.get('system_recommendations', []),
            "optimization_summary": self.performance_optimizer.get_optimization_summary()
        }
        
        print(f"    Optimization time: {optimization_time:.3f}s")
        print(f"    Optimizations applied: {list(optimizations.keys())}")
        print(f"    System recommendations: {len(result['system_recommendations'])}")
        
        print("✅ End-to-end optimization tests completed")
        return {"end_to_end_test": result}
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report."""
        print("\n📊 Generating Validation Report...")
        
        # Run all tests
        compression_results = self.test_compression_optimization()
        chunk_results = self.test_chunk_size_optimization()
        tracking_results = self.test_performance_tracking_accuracy()
        e2e_results = self.test_end_to_end_optimization()
        
        # Compile final report
        report = {
            "validation_timestamp": time.time(),
            "test_results": {
                **compression_results,
                **chunk_results,
                **tracking_results,
                **e2e_results
            },
            "summary": {
                "total_tests": 4,
                "optimization_features_tested": [
                    "adaptive_compression",
                    "progressive_chunk_sizing", 
                    "performance_tracking",
                    "end_to_end_integration"
                ]
            }
        }
        
        # Save report
        report_file = Path("data/logs/optimization_validation_report.json")
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Validation report saved to: {report_file}")
        return report


def main():
    """Main validation function."""
    print("🔍 TNGD Backup System - Performance Optimization Validation")
    print("=" * 65)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    validator = PerformanceValidator()
    
    try:
        # Run validation
        report = validator.generate_report()
        
        # Display summary
        print("\n" + "=" * 65)
        print("🎯 Validation Summary:")
        print(f"   Tests completed: {report['summary']['total_tests']}")
        print(f"   Features validated: {len(report['summary']['optimization_features_tested'])}")
        
        # Check for any issues
        issues = []
        
        # Check compression tests
        compression_tests = report['test_results'].get('compression_tests', [])
        for test in compression_tests:
            if not test.get('adaptive', {}).get('success', False):
                issues.append(f"Compression failed for {test['scenario']}")
        
        # Check tracking accuracy
        tracking_tests = report['test_results'].get('tracking_tests', [])
        for test in tracking_tests:
            if test.get('duration_accuracy', 0) < 0.95:
                issues.append(f"Low tracking accuracy for {test['operation']}")
        
        if issues:
            print("\n⚠️ Issues found:")
            for issue in issues:
                print(f"   - {issue}")
            return 1
        else:
            print("\n✅ All validation tests passed successfully!")
            print("\nYour optimization features are working correctly and ready for production use.")
            return 0
    
    except Exception as e:
        print(f"\n❌ Validation failed with error: {e}")
        return 1
    
    finally:
        validator.cleanup()


if __name__ == "__main__":
    sys.exit(main())
