#!/usr/bin/env python3
"""
Performance Tracking and Optimization Module

This module provides comprehensive performance tracking, analysis, and optimization
recommendations for the TNGD backup system.

Features:
- Real-time performance monitoring
- Adaptive optimization recommendations
- Performance trend analysis
- Resource utilization tracking
- Bottleneck identification
"""

import time
import logging
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque

# Import system monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


@dataclass
class PerformanceMetric:
    """Individual performance metric data point."""
    timestamp: float
    operation: str
    duration: float
    throughput: float  # rows/second or MB/second
    memory_usage_mb: float
    cpu_percent: float
    success: bool
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceAnalysis:
    """Performance analysis results."""
    operation: str
    total_operations: int
    success_rate: float
    avg_duration: float
    avg_throughput: float
    avg_memory_usage: float
    avg_cpu_usage: float
    bottlenecks: List[str]
    recommendations: List[str]
    trend: str  # 'improving', 'degrading', 'stable'


class PerformanceTracker:
    """
    Comprehensive performance tracking and analysis system.
    
    Tracks performance metrics across all backup operations and provides
    intelligent optimization recommendations.
    """
    
    def __init__(self, max_history: int = 1000):
        """Initialize performance tracker."""
        self.logger = logging.getLogger(f"{__name__}.PerformanceTracker")
        self.max_history = max_history
        
        # Performance data storage
        self.metrics: deque = deque(maxlen=max_history)
        self.operation_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=200))
        
        # Analysis cache
        self.last_analysis_time = 0
        self.analysis_cache: Dict[str, PerformanceAnalysis] = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Performance baselines
        self.baselines: Dict[str, Dict[str, float]] = {}
        self.baseline_samples = 10  # Number of samples to establish baseline
        
        # System monitoring
        self.system_metrics_enabled = PSUTIL_AVAILABLE
        if not self.system_metrics_enabled:
            self.logger.warning("psutil not available - system metrics disabled")
    
    def record_operation(self, operation: str, duration: float, 
                        throughput: float = 0, success: bool = True,
                        metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Record performance metrics for an operation.
        
        Args:
            operation: Name of the operation
            duration: Duration in seconds
            throughput: Throughput (rows/sec or MB/sec)
            success: Whether operation succeeded
            metadata: Additional metadata
        """
        timestamp = time.time()
        
        # Get system metrics if available
        memory_usage = 0
        cpu_percent = 0
        
        if self.system_metrics_enabled:
            try:
                process = psutil.Process()
                memory_usage = process.memory_info().rss / 1024 / 1024  # MB
                cpu_percent = process.cpu_percent()
            except Exception as e:
                self.logger.debug(f"Failed to get system metrics: {e}")
        
        # Create metric record
        metric = PerformanceMetric(
            timestamp=timestamp,
            operation=operation,
            duration=duration,
            throughput=throughput,
            memory_usage_mb=memory_usage,
            cpu_percent=cpu_percent,
            success=success,
            metadata=metadata or {}
        )
        
        # Store metrics
        self.metrics.append(metric)
        self.operation_metrics[operation].append(metric)
        
        # Update baselines if needed
        self._update_baselines(operation, metric)
        
        # Log performance if significant
        if duration > 30 or not success:
            level = logging.WARNING if not success else logging.INFO
            self.logger.log(level, f"Performance: {operation} took {duration:.1f}s "
                          f"(throughput: {throughput:.1f}, success: {success})")
    
    def _update_baselines(self, operation: str, metric: PerformanceMetric) -> None:
        """Update performance baselines for the operation."""
        if operation not in self.baselines:
            self.baselines[operation] = {
                'duration_samples': [],
                'throughput_samples': [],
                'memory_samples': []
            }
        
        baseline = self.baselines[operation]
        
        # Add samples if successful operation
        if metric.success:
            baseline['duration_samples'].append(metric.duration)
            baseline['throughput_samples'].append(metric.throughput)
            baseline['memory_samples'].append(metric.memory_usage_mb)
            
            # Keep only recent samples for baseline
            for key in baseline:
                if len(baseline[key]) > self.baseline_samples:
                    baseline[key] = baseline[key][-self.baseline_samples:]
    
    def analyze_performance(self, operation: str = None, 
                          force_refresh: bool = False) -> Dict[str, PerformanceAnalysis]:
        """
        Analyze performance metrics and generate recommendations.
        
        Args:
            operation: Specific operation to analyze (None for all)
            force_refresh: Force refresh of analysis cache
            
        Returns:
            Dictionary of performance analyses by operation
        """
        current_time = time.time()
        
        # Check cache validity
        if (not force_refresh and 
            current_time - self.last_analysis_time < self.cache_ttl and
            self.analysis_cache):
            
            if operation:
                return {operation: self.analysis_cache.get(operation)} if operation in self.analysis_cache else {}
            return self.analysis_cache
        
        # Perform fresh analysis
        analyses = {}
        operations_to_analyze = [operation] if operation else self.operation_metrics.keys()
        
        for op in operations_to_analyze:
            if op in self.operation_metrics and self.operation_metrics[op]:
                analyses[op] = self._analyze_operation(op)
        
        # Update cache
        if not operation:  # Full analysis
            self.analysis_cache = analyses
            self.last_analysis_time = current_time
        else:  # Partial analysis
            self.analysis_cache[operation] = analyses.get(operation)
        
        return analyses
    
    def _analyze_operation(self, operation: str) -> PerformanceAnalysis:
        """Analyze performance for a specific operation."""
        metrics = list(self.operation_metrics[operation])
        
        if not metrics:
            return PerformanceAnalysis(
                operation=operation,
                total_operations=0,
                success_rate=0,
                avg_duration=0,
                avg_throughput=0,
                avg_memory_usage=0,
                avg_cpu_usage=0,
                bottlenecks=[],
                recommendations=["No performance data available"],
                trend="unknown"
            )
        
        # Calculate basic statistics
        successful_metrics = [m for m in metrics if m.success]
        total_operations = len(metrics)
        success_rate = len(successful_metrics) / total_operations if total_operations > 0 else 0
        
        if successful_metrics:
            avg_duration = statistics.mean(m.duration for m in successful_metrics)
            avg_throughput = statistics.mean(m.throughput for m in successful_metrics)
            avg_memory_usage = statistics.mean(m.memory_usage_mb for m in successful_metrics)
            avg_cpu_usage = statistics.mean(m.cpu_percent for m in successful_metrics)
        else:
            avg_duration = avg_throughput = avg_memory_usage = avg_cpu_usage = 0
        
        # Identify bottlenecks
        bottlenecks = self._identify_bottlenecks(operation, successful_metrics)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(operation, successful_metrics, bottlenecks)
        
        # Determine trend
        trend = self._analyze_trend(successful_metrics)
        
        return PerformanceAnalysis(
            operation=operation,
            total_operations=total_operations,
            success_rate=success_rate,
            avg_duration=avg_duration,
            avg_throughput=avg_throughput,
            avg_memory_usage=avg_memory_usage,
            avg_cpu_usage=avg_cpu_usage,
            bottlenecks=bottlenecks,
            recommendations=recommendations,
            trend=trend
        )
    
    def _identify_bottlenecks(self, operation: str, metrics: List[PerformanceMetric]) -> List[str]:
        """Identify performance bottlenecks for an operation."""
        bottlenecks = []
        
        if not metrics:
            return bottlenecks
        
        # Check against baselines if available
        baseline = self.baselines.get(operation, {})
        
        # Duration bottleneck
        if baseline.get('duration_samples'):
            baseline_duration = statistics.mean(baseline['duration_samples'])
            recent_duration = statistics.mean(m.duration for m in metrics[-5:])  # Last 5 operations
            
            if recent_duration > baseline_duration * 1.5:  # 50% slower than baseline
                bottlenecks.append("Duration significantly slower than baseline")
        
        # Memory bottleneck
        memory_values = [m.memory_usage_mb for m in metrics if m.memory_usage_mb > 0]
        if memory_values:
            max_memory = max(memory_values)
            avg_memory = statistics.mean(memory_values)
            
            if max_memory > 2000:  # > 2GB
                bottlenecks.append("High memory usage detected")
            elif avg_memory > 1000:  # > 1GB average
                bottlenecks.append("Consistently high memory usage")
        
        # CPU bottleneck
        cpu_values = [m.cpu_percent for m in metrics if m.cpu_percent > 0]
        if cpu_values:
            avg_cpu = statistics.mean(cpu_values)
            if avg_cpu > 80:
                bottlenecks.append("High CPU usage")
        
        # Throughput bottleneck
        throughput_values = [m.throughput for m in metrics if m.throughput > 0]
        if throughput_values and baseline.get('throughput_samples'):
            baseline_throughput = statistics.mean(baseline['throughput_samples'])
            recent_throughput = statistics.mean(throughput_values[-5:])
            
            if recent_throughput < baseline_throughput * 0.7:  # 30% slower
                bottlenecks.append("Throughput significantly below baseline")
        
        return bottlenecks
    
    def _generate_recommendations(self, operation: str, metrics: List[PerformanceMetric], 
                                bottlenecks: List[str]) -> List[str]:
        """Generate optimization recommendations based on performance analysis."""
        recommendations = []
        
        if not metrics:
            return ["Insufficient data for recommendations"]
        
        # Memory-based recommendations
        memory_values = [m.memory_usage_mb for m in metrics if m.memory_usage_mb > 0]
        if memory_values:
            avg_memory = statistics.mean(memory_values)
            max_memory = max(memory_values)
            
            if max_memory > 2000:
                recommendations.append("Consider reducing chunk sizes to lower memory usage")
            elif avg_memory > 1000:
                recommendations.append("Enable aggressive memory management and garbage collection")
        
        # Duration-based recommendations
        durations = [m.duration for m in metrics]
        if durations:
            avg_duration = statistics.mean(durations)
            
            if avg_duration > 300:  # > 5 minutes
                recommendations.append("Consider parallel processing or smaller batch sizes")
            elif avg_duration > 60:  # > 1 minute
                recommendations.append("Monitor for potential optimizations in data processing")
        
        # Success rate recommendations
        success_rate = sum(1 for m in metrics if m.success) / len(metrics)
        if success_rate < 0.9:
            recommendations.append("Investigate frequent failures and improve error handling")
        elif success_rate < 0.95:
            recommendations.append("Consider implementing more robust retry mechanisms")
        
        # Throughput recommendations
        throughput_values = [m.throughput for m in metrics if m.throughput > 0]
        if throughput_values:
            recent_throughput = statistics.mean(throughput_values[-5:])
            
            if recent_throughput < 1000:  # < 1000 rows/sec
                recommendations.append("Consider optimizing query performance or increasing parallelism")
        
        # Bottleneck-specific recommendations
        for bottleneck in bottlenecks:
            if "memory" in bottleneck.lower():
                recommendations.append("Implement streaming processing for large datasets")
            elif "cpu" in bottleneck.lower():
                recommendations.append("Consider reducing processing complexity or adding delays")
            elif "throughput" in bottleneck.lower():
                recommendations.append("Optimize database queries and network connections")
        
        return recommendations or ["Performance appears optimal"]
    
    def _analyze_trend(self, metrics: List[PerformanceMetric]) -> str:
        """Analyze performance trend over time."""
        if len(metrics) < 5:
            return "insufficient_data"
        
        # Split metrics into two halves for comparison
        mid_point = len(metrics) // 2
        first_half = metrics[:mid_point]
        second_half = metrics[mid_point:]
        
        first_avg_duration = statistics.mean(m.duration for m in first_half)
        second_avg_duration = statistics.mean(m.duration for m in second_half)
        
        # Determine trend based on duration change
        change_ratio = second_avg_duration / first_avg_duration if first_avg_duration > 0 else 1
        
        if change_ratio > 1.1:  # 10% slower
            return "degrading"
        elif change_ratio < 0.9:  # 10% faster
            return "improving"
        else:
            return "stable"
    
    def get_system_recommendations(self) -> List[str]:
        """Get system-wide optimization recommendations."""
        recommendations = []
        
        # Analyze all operations
        analyses = self.analyze_performance()
        
        if not analyses:
            return ["No performance data available for analysis"]
        
        # Overall success rate
        total_operations = sum(a.total_operations for a in analyses.values())
        total_successful = sum(a.total_operations * a.success_rate for a in analyses.values())
        overall_success_rate = total_successful / total_operations if total_operations > 0 else 0
        
        if overall_success_rate < 0.9:
            recommendations.append("System-wide success rate is low - review error handling and retry logic")
        
        # Memory usage patterns
        high_memory_operations = [name for name, analysis in analyses.items() 
                                if analysis.avg_memory_usage > 1000]
        if high_memory_operations:
            recommendations.append(f"High memory usage in operations: {', '.join(high_memory_operations)}")
        
        # Performance trends
        degrading_operations = [name for name, analysis in analyses.items() 
                              if analysis.trend == "degrading"]
        if degrading_operations:
            recommendations.append(f"Performance degrading in: {', '.join(degrading_operations)}")
        
        # Common bottlenecks
        all_bottlenecks = []
        for analysis in analyses.values():
            all_bottlenecks.extend(analysis.bottlenecks)
        
        if all_bottlenecks:
            bottleneck_counts = defaultdict(int)
            for bottleneck in all_bottlenecks:
                bottleneck_counts[bottleneck] += 1
            
            common_bottlenecks = [b for b, count in bottleneck_counts.items() if count > 1]
            if common_bottlenecks:
                recommendations.append(f"Common bottlenecks: {', '.join(common_bottlenecks)}")
        
        return recommendations or ["System performance appears optimal"]
    
    def export_metrics(self, operation: str = None, 
                      start_time: float = None, end_time: float = None) -> List[Dict[str, Any]]:
        """Export performance metrics for external analysis."""
        metrics_to_export = []
        
        for metric in self.metrics:
            # Filter by operation if specified
            if operation and metric.operation != operation:
                continue
            
            # Filter by time range if specified
            if start_time and metric.timestamp < start_time:
                continue
            if end_time and metric.timestamp > end_time:
                continue
            
            metrics_to_export.append({
                'timestamp': metric.timestamp,
                'datetime': datetime.fromtimestamp(metric.timestamp).isoformat(),
                'operation': metric.operation,
                'duration': metric.duration,
                'throughput': metric.throughput,
                'memory_usage_mb': metric.memory_usage_mb,
                'cpu_percent': metric.cpu_percent,
                'success': metric.success,
                'metadata': metric.metadata
            })
        
        return metrics_to_export
