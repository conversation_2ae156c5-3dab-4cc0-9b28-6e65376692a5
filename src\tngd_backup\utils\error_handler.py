#!/usr/bin/env python3
"""
Standardized Error Handling Utilities for TNGD Backup System

This module provides consistent error handling patterns, logging,
and recovery mechanisms across the entire codebase.
"""

import logging
import traceback
import functools
import time
from typing import Any, Callable, Dict, List, Optional, Type, Union, Tuple
from ..constants import ErrorConstants


class BackupError(Exception):
    """Base exception for all backup-related errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.error_code = error_code
        self.context = context or {}


class ApiError(BackupError):
    """API-related errors."""
    pass


class NetworkError(BackupError):
    """Network-related errors."""
    pass


class ConfigurationError(BackupError):
    """Configuration-related errors."""
    pass


class ResourceError(BackupError):
    """Resource exhaustion errors."""
    pass


class ValidationError(BackupError):
    """Data validation errors."""
    pass


class ErrorHandler:
    """Centralized error handling utility."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

        # Smart retry configuration
        self.retry_strategies = {
            'network': {'max_retries': 5, 'base_delay': 2, 'backoff_factor': 2, 'max_delay': 60},
            'memory': {'max_retries': 3, 'base_delay': 5, 'backoff_factor': 1.5, 'max_delay': 30},
            'timeout': {'max_retries': 4, 'base_delay': 10, 'backoff_factor': 1.8, 'max_delay': 120},
            'resource': {'max_retries': 3, 'base_delay': 15, 'backoff_factor': 2, 'max_delay': 180},
            'validation': {'max_retries': 2, 'base_delay': 1, 'backoff_factor': 1, 'max_delay': 5},
            'default': {'max_retries': 3, 'base_delay': 5, 'backoff_factor': 2, 'max_delay': 60}
        }
        self.retry_history = []
        self.error_counts = {}
        self.last_errors = {}
    
    def is_retryable_error(self, error: Exception) -> bool:
        """Determine if an error is retryable."""
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        # Check against retryable error types
        if error_type in ErrorConstants.RETRYABLE_ERRORS:
            return True
        
        # Check against non-retryable error types
        if error_type in ErrorConstants.NON_RETRYABLE_ERRORS:
            return False
        
        # Check error message for retryable patterns
        retryable_patterns = [
            'timeout', 'connection', 'network', 'temporary',
            'service unavailable', 'too many requests', 'rate limit'
        ]
        
        for pattern in retryable_patterns:
            if pattern in error_message:
                return True
        
        # Check for non-retryable patterns
        non_retryable_patterns = [
            'authentication', 'permission', 'unauthorized',
            'forbidden', 'not found', 'invalid', 'malformed'
        ]
        
        for pattern in non_retryable_patterns:
            if pattern in error_message:
                return False
        
        # Default to retryable for unknown errors
        return True
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None,
                  level: int = logging.ERROR) -> None:
        """Log an error with consistent formatting."""
        error_type = type(error).__name__
        error_message = str(error)
        
        # Build log message
        log_parts = [f"{error_type}: {error_message}"]
        
        # Add context if provided
        if context:
            context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
            log_parts.append(f"Context: {context_str}")
        
        # Add stack trace for ERROR level and above
        if level >= logging.ERROR:
            log_parts.append(f"Stack trace: {traceback.format_exc()}")
        
        self.logger.log(level, " | ".join(log_parts))
    
    def handle_error(self, error: Exception, operation: str,
                    context: Optional[Dict[str, Any]] = None,
                    reraise: bool = True) -> None:
        """Handle an error with consistent logging and optional re-raising."""
        enhanced_context = {"operation": operation}
        if context:
            enhanced_context.update(context)
        
        self.log_error(error, enhanced_context)
        
        if reraise:
            raise
    
    def create_error_result(self, error: Exception, operation: str,
                           context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a standardized error result dictionary."""
        return {
            "status": "error",
            "error": str(error),
            "error_type": type(error).__name__,
            "operation": operation,
            "retryable": self.is_retryable_error(error),
            "context": context or {},
            "timestamp": None  # Will be set by caller if needed
        }

    def classify_error(self, error: Exception) -> str:
        """Classify error type for appropriate retry strategy."""
        error_type = type(error).__name__
        error_message = str(error).lower()

        # Network-related errors
        if any(pattern in error_message for pattern in [
            'connection', 'network', 'dns', 'socket', 'timeout', 'unreachable',
            'connection refused', 'connection reset', 'connection timeout'
        ]) or error_type in ['ConnectionError', 'TimeoutError', 'URLError']:
            return 'network'

        # Memory-related errors
        if any(pattern in error_message for pattern in [
            'memory', 'out of memory', 'memoryerror', 'allocation failed'
        ]) or error_type in ['MemoryError', 'ResourceError']:
            return 'memory'

        # Timeout-specific errors
        if any(pattern in error_message for pattern in [
            'timeout', 'timed out', 'deadline exceeded'
        ]) or error_type in ['TimeoutError', 'ReadTimeoutError']:
            return 'timeout'

        # Resource-related errors
        if any(pattern in error_message for pattern in [
            'resource', 'busy', 'locked', 'unavailable', 'throttled',
            'rate limit', 'quota exceeded', 'too many requests'
        ]) or error_type in ['ResourceError', 'ThrottlingError']:
            return 'resource'

        # Validation errors
        if any(pattern in error_message for pattern in [
            'validation', 'invalid', 'malformed', 'bad request'
        ]) or error_type in ['ValidationError', 'ValueError']:
            return 'validation'

        return 'default'

    def get_retry_strategy(self, error: Exception) -> Dict[str, Any]:
        """Get appropriate retry strategy for the given error."""
        error_category = self.classify_error(error)
        strategy = self.retry_strategies.get(error_category, self.retry_strategies['default'])

        # Adjust strategy based on error history
        error_key = f"{type(error).__name__}:{str(error)[:50]}"
        error_count = self.error_counts.get(error_key, 0)

        # If this error has occurred frequently, be more conservative
        if error_count > 3:
            strategy = strategy.copy()
            strategy['max_retries'] = max(1, strategy['max_retries'] - 1)
            strategy['base_delay'] *= 1.5

        return strategy

    def calculate_retry_delay(self, attempt: int, strategy: Dict[str, Any]) -> float:
        """Calculate delay for retry attempt using exponential backoff."""
        base_delay = strategy['base_delay']
        backoff_factor = strategy['backoff_factor']
        max_delay = strategy['max_delay']

        # Exponential backoff with jitter
        delay = base_delay * (backoff_factor ** (attempt - 1))

        # Add jitter (±20% randomization)
        import random
        jitter = delay * 0.2 * (random.random() - 0.5)
        delay += jitter

        # Cap at maximum delay
        return min(delay, max_delay)

    def should_retry(self, error: Exception, attempt: int, operation: str = "unknown") -> Tuple[bool, float]:
        """
        Determine if operation should be retried and calculate delay.

        Returns:
            Tuple of (should_retry, delay_seconds)
        """
        if not self.is_retryable_error(error):
            return False, 0

        strategy = self.get_retry_strategy(error)

        if attempt >= strategy['max_retries']:
            return False, 0

        delay = self.calculate_retry_delay(attempt, strategy)

        # Record retry decision
        retry_record = {
            'timestamp': time.time(),
            'operation': operation,
            'error_type': type(error).__name__,
            'error_message': str(error)[:100],
            'attempt': attempt,
            'strategy': strategy,
            'delay': delay
        }

        self.retry_history.append(retry_record)

        # Keep only recent retry history
        if len(self.retry_history) > 100:
            self.retry_history = self.retry_history[-100:]

        # Update error counts
        error_key = f"{type(error).__name__}:{str(error)[:50]}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        self.last_errors[error_key] = time.time()

        self.logger.info(f"Retry {attempt}/{strategy['max_retries']} for {operation}: "
                        f"{type(error).__name__} (delay: {delay:.1f}s)")

        return True, delay

    def get_retry_statistics(self) -> Dict[str, Any]:
        """Get retry statistics and performance metrics."""
        if not self.retry_history:
            return {"message": "No retry history available"}

        recent_retries = [r for r in self.retry_history if time.time() - r['timestamp'] < 3600]  # Last hour

        error_types = {}
        total_delays = 0
        successful_strategies = {}

        for retry in recent_retries:
            error_type = retry['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1
            total_delays += retry['delay']

            strategy_key = retry['strategy']['max_retries']
            successful_strategies[strategy_key] = successful_strategies.get(strategy_key, 0) + 1

        return {
            'total_retries_last_hour': len(recent_retries),
            'most_common_error_types': sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:5],
            'average_retry_delay': total_delays / len(recent_retries) if recent_retries else 0,
            'strategy_usage': successful_strategies,
            'error_count_by_type': self.error_counts
        }


def smart_retry(operation_name: str = "unknown", logger: Optional[logging.Logger] = None):
    """
    Decorator for smart retry logic with adaptive strategies.

    Args:
        operation_name: Name of the operation for logging
        logger: Optional logger instance
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = ErrorHandler(logger)
            attempt = 0
            last_error = None

            while True:
                attempt += 1

                try:
                    return func(*args, **kwargs)

                except Exception as e:
                    last_error = e

                    # Check if we should retry
                    should_retry, delay = error_handler.should_retry(e, attempt, operation_name)

                    if not should_retry:
                        error_handler.logger.error(f"Max retries exceeded for {operation_name}: {type(e).__name__}: {str(e)}")
                        raise

                    # Log retry attempt
                    error_handler.logger.warning(f"Retry {attempt} for {operation_name} after {type(e).__name__}: {str(e)}")

                    # Wait before retry
                    if delay > 0:
                        time.sleep(delay)

            # This should never be reached, but just in case
            if last_error:
                raise last_error

        return wrapper
    return decorator


def with_error_handling(operation_name: str, logger: Optional[logging.Logger] = None,
                       reraise: bool = True, return_on_error: Any = None):
    """Decorator for consistent error handling."""
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = ErrorHandler(logger)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys())
                }
                
                error_handler.handle_error(e, operation_name, context, reraise=False)
                
                if reraise:
                    raise
                else:
                    return return_on_error
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return: Any = None,
                logger: Optional[logging.Logger] = None, **kwargs) -> Any:
    """Safely execute a function with error handling."""
    error_handler = ErrorHandler(logger)
    
    try:
        return func(*args, **kwargs)
    except Exception as e:
        context = {
            "function": func.__name__ if hasattr(func, '__name__') else str(func),
            "args": str(args)[:100],  # Truncate long args
            "kwargs": str(kwargs)[:100]  # Truncate long kwargs
        }
        
        error_handler.log_error(e, context, level=logging.WARNING)
        return default_return


# Pre-configured error handlers for common operations
def get_api_error_handler(logger: Optional[logging.Logger] = None) -> ErrorHandler:
    """Get error handler configured for API operations."""
    return ErrorHandler(logger)


def get_storage_error_handler(logger: Optional[logging.Logger] = None) -> ErrorHandler:
    """Get error handler configured for storage operations."""
    return ErrorHandler(logger)


def get_processing_error_handler(logger: Optional[logging.Logger] = None) -> ErrorHandler:
    """Get error handler configured for data processing operations."""
    return ErrorHandler(logger)
