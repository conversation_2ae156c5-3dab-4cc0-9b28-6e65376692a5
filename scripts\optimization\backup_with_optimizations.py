#!/usr/bin/env python3
"""
Optimized Backup Runner

This script runs the backup system with all performance optimizations enabled.
It demonstrates how to integrate the optimization features with the existing backup engine.
"""

import os
import sys
import time
import logging
import argparse
from pathlib import Path
from typing import Dict, Any

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from tngd_backup.core.performance_optimizer import PerformanceOptimizer
from tngd_backup.core.backup_engine import BackupEngine
from tngd_backup.utils.monitoring import ResourceMonitor
from tngd_backup.utils.error_handler import smart_retry


class OptimizedBackupRunner:
    """
    Backup runner with integrated performance optimizations.
    
    This class wraps the existing backup engine and adds optimization features
    including progressive chunk sizing, adaptive compression, smart throttling,
    and performance tracking.
    """
    
    def __init__(self, config_path: str = None):
        """Initialize optimized backup runner."""
        self.logger = logging.getLogger(f"{__name__}.OptimizedBackupRunner")
        
        # Initialize optimization components
        self.performance_optimizer = PerformanceOptimizer()
        self.resource_monitor = ResourceMonitor()
        
        # Initialize backup engine (assuming it exists)
        self.backup_engine = None
        self.config_path = config_path
        
        # Performance tracking
        self.operation_start_times = {}
        self.current_operation_context = {}
        
        self.logger.info("Optimized backup runner initialized")
    
    def setup_backup_engine(self):
        """Setup the backup engine with optimization integration."""
        try:
            # This would integrate with your existing BackupEngine
            # For now, we'll create a placeholder that shows the integration pattern
            self.logger.info("Setting up backup engine with optimizations...")
            
            # Enable performance tracking in backup engine
            if hasattr(self.backup_engine, 'enable_performance_tracking'):
                self.backup_engine.enable_performance_tracking(True)
            
            # Configure adaptive settings
            optimization_summary = self.performance_optimizer.get_optimization_summary()
            self.logger.info(f"Optimization features: {list(optimization_summary.keys())}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup backup engine: {e}")
            return False
    
    @smart_retry("backup_table")
    def backup_table_optimized(self, table_name: str, date_range: tuple) -> Dict[str, Any]:
        """
        Backup a table with all optimizations applied.
        
        Args:
            table_name: Name of the table to backup
            date_range: Tuple of (start_date, end_date)
            
        Returns:
            Backup result dictionary
        """
        operation_start = time.time()
        operation_name = f"backup_table_{table_name}"
        
        try:
            self.logger.info(f"Starting optimized backup for table: {table_name}")
            
            # Record operation start
            self.operation_start_times[operation_name] = operation_start
            
            # Get initial system state
            initial_health = self.resource_monitor.get_system_health()
            self.logger.info(f"Initial system state: CPU={initial_health.cpu_percent:.1f}%, "
                           f"Memory={initial_health.memory_percent:.1f}%")
            
            # Prepare operation context for optimizations
            operation_context = {
                'operation': operation_name,
                'table_name': table_name,
                'date_range': date_range,
                'initial_health': initial_health
            }
            
            # Step 1: Determine optimal chunk size
            chunk_size = self._get_optimized_chunk_size(table_name, operation_context)
            operation_context['chunk_size'] = chunk_size
            
            # Step 2: Apply throttling if needed
            self._apply_smart_throttling(operation_context)
            
            # Step 3: Execute backup with optimizations
            result = self._execute_backup_with_monitoring(table_name, date_range, operation_context)
            
            # Step 4: Record performance metrics
            duration = time.time() - operation_start
            throughput = result.get('rows_processed', 0) / duration if duration > 0 else 0
            
            self.performance_optimizer.record_operation_performance(
                operation_name, duration, throughput, result.get('success', False),
                {'table_name': table_name, 'chunk_size': chunk_size}
            )
            
            # Step 5: Apply post-operation optimizations
            self._post_operation_optimization(operation_context, result)
            
            self.logger.info(f"Completed backup for {table_name}: "
                           f"{duration:.1f}s, {throughput:.0f} rows/s")
            
            return result
            
        except Exception as e:
            duration = time.time() - operation_start
            self.performance_optimizer.record_operation_performance(
                operation_name, duration, 0, False, {'error': str(e)}
            )
            self.logger.error(f"Backup failed for {table_name}: {e}")
            raise
    
    def _get_optimized_chunk_size(self, table_name: str, context: Dict[str, Any]) -> int:
        """Get optimized chunk size for the table."""
        # Get historical performance data for this table
        analysis = self.performance_optimizer.get_performance_analysis(f"backup_table_{table_name}")
        
        # Default chunk size
        default_size = 50000
        
        if analysis.get('operation_analyses'):
            table_analysis = analysis['operation_analyses'].get(f"backup_table_{table_name}")
            if table_analysis:
                performance_data = {
                    'avg_duration': table_analysis.avg_duration,
                    'success_rate': table_analysis.success_rate,
                    'memory_pressure': context['initial_health'].memory_percent
                }
                
                optimized_size = self.performance_optimizer.optimize_chunk_size(
                    f"backup_table_{table_name}", default_size, performance_data
                )
                
                self.logger.info(f"Optimized chunk size for {table_name}: {optimized_size}")
                return optimized_size
        
        return default_size
    
    def _apply_smart_throttling(self, context: Dict[str, Any]) -> None:
        """Apply smart throttling based on system conditions."""
        should_throttle, throttle_actions = self.performance_optimizer.should_apply_throttling()
        
        if should_throttle:
            self.logger.warning(f"Applying throttling for {context['operation']}: {throttle_actions}")
            self.resource_monitor.apply_throttling(context['operation'])
            
            # Update context with throttling info
            context['throttling_applied'] = throttle_actions
    
    def _execute_backup_with_monitoring(self, table_name: str, date_range: tuple, 
                                      context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute backup with continuous monitoring and adaptive adjustments."""
        
        # This is where you would integrate with your existing backup logic
        # For demonstration, we'll simulate the backup process
        
        self.logger.info(f"Executing backup for {table_name} with monitoring...")
        
        # Simulate backup process with monitoring
        chunk_count = 0
        total_rows = 0
        
        # Get compression settings
        data_characteristics = {
            'size_mb': 100,  # This would come from actual data analysis
            'compressibility': 'high',  # Based on table type
            'data_type': 'text'
        }
        
        compression_settings = self.performance_optimizer.optimize_compression(data_characteristics)
        self.logger.info(f"Using compression settings: {compression_settings}")
        
        # Simulate processing chunks
        for chunk_id in range(1, 6):  # Simulate 5 chunks
            chunk_start = time.time()
            
            # Check for memory pressure during processing
            current_health = self.resource_monitor.get_system_health()
            if current_health.memory_percent > 85:
                self.logger.warning("High memory pressure detected during backup")
                self.resource_monitor.apply_throttling(f"{context['operation']}_chunk_{chunk_id}")
            
            # Simulate chunk processing
            time.sleep(0.1)  # Simulate work
            chunk_rows = context['chunk_size']
            total_rows += chunk_rows
            chunk_count += 1
            
            chunk_duration = time.time() - chunk_start
            self.logger.debug(f"Processed chunk {chunk_id}: {chunk_rows} rows in {chunk_duration:.2f}s")
        
        # Return backup result
        return {
            'success': True,
            'table_name': table_name,
            'chunks_processed': chunk_count,
            'rows_processed': total_rows,
            'compression_settings': compression_settings,
            'throttling_applied': context.get('throttling_applied', {})
        }
    
    def _post_operation_optimization(self, context: Dict[str, Any], result: Dict[str, Any]) -> None:
        """Apply post-operation optimizations based on results."""
        
        # Update performance data for future optimizations
        context['performance_data'] = {
            'success_rate': 1.0 if result.get('success') else 0.0,
            'rows_processed': result.get('rows_processed', 0),
            'chunks_processed': result.get('chunks_processed', 0)
        }
        
        # Apply optimizations for next operation
        optimizations = self.performance_optimizer.apply_optimizations(context)
        
        if optimizations:
            self.logger.info(f"Applied post-operation optimizations: {list(optimizations.keys())}")
    
    def run_backup(self, tables: list = None, date_range: tuple = None) -> Dict[str, Any]:
        """
        Run complete backup with optimizations.
        
        Args:
            tables: List of tables to backup (None for all)
            date_range: Date range tuple (start, end)
            
        Returns:
            Overall backup results
        """
        backup_start = time.time()
        
        try:
            # Setup backup engine
            if not self.setup_backup_engine():
                raise Exception("Failed to setup backup engine")
            
            # Start resource monitoring
            self.resource_monitor.start_monitoring()
            
            # Default tables if none specified
            if tables is None:
                tables = ['sample.table1', 'sample.table2']  # This would come from config
            
            # Default date range if none specified
            if date_range is None:
                from datetime import datetime, timedelta
                end_date = datetime.now()
                start_date = end_date - timedelta(days=1)
                date_range = (start_date, end_date)
            
            results = {}
            successful_tables = 0
            
            # Backup each table with optimizations
            for table in tables:
                try:
                    table_result = self.backup_table_optimized(table, date_range)
                    results[table] = table_result
                    
                    if table_result.get('success'):
                        successful_tables += 1
                        
                except Exception as e:
                    self.logger.error(f"Failed to backup table {table}: {e}")
                    results[table] = {'success': False, 'error': str(e)}
            
            # Generate final performance analysis
            total_duration = time.time() - backup_start
            analysis = self.performance_optimizer.get_performance_analysis()
            
            # Stop monitoring
            self.resource_monitor.stop_monitoring()
            
            # Compile final results
            final_results = {
                'success': successful_tables == len(tables),
                'total_duration': total_duration,
                'tables_processed': len(tables),
                'successful_tables': successful_tables,
                'table_results': results,
                'performance_analysis': analysis,
                'optimization_summary': self.performance_optimizer.get_optimization_summary()
            }
            
            self.logger.info(f"Backup completed: {successful_tables}/{len(tables)} tables successful")
            return final_results
            
        except Exception as e:
            self.logger.error(f"Backup failed: {e}")
            return {'success': False, 'error': str(e)}


def main():
    """Main function for running optimized backup."""
    parser = argparse.ArgumentParser(description='Run backup with performance optimizations')
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--tables', nargs='+', help='Specific tables to backup')
    parser.add_argument('--date', help='Date to backup (YYYY-MM-DD)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create backup runner
    runner = OptimizedBackupRunner(args.config)
    
    # Parse date range if provided
    date_range = None
    if args.date:
        from datetime import datetime
        target_date = datetime.strptime(args.date, '%Y-%m-%d')
        date_range = (target_date, target_date)
    
    # Run backup
    print("🚀 Starting optimized backup...")
    results = runner.run_backup(args.tables, date_range)
    
    # Display results
    if results.get('success'):
        print(f"✅ Backup completed successfully in {results['total_duration']:.1f}s")
        print(f"   Tables: {results['successful_tables']}/{results['tables_processed']}")
        
        # Show optimization summary
        opt_summary = results.get('optimization_summary', {})
        enabled_features = [k for k, v in opt_summary.items() 
                          if isinstance(v, dict) and v.get('enabled')]
        print(f"   Optimizations: {', '.join(enabled_features)}")
    else:
        print(f"❌ Backup failed: {results.get('error', 'Unknown error')}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
