#!/usr/bin/env python3
"""
Thread Manager Module

This module provides improved thread management for the TNGD backup system
to prevent "can't start new thread" errors and resource exhaustion.

Features:
- Thread pool management with limits
- Resource monitoring and throttling
- Automatic cleanup and garbage collection
- Thread health monitoring
"""

import threading
import time
import logging
import gc
import psutil
import os
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Dict, Optional, List, Callable, Any
from dataclasses import dataclass
from contextlib import contextmanager
from enum import Enum

from ..constants import BackupConstants

logger = logging.getLogger(__name__)


class PoolStrategy(Enum):
    """Thread pool management strategies."""
    MULTIPLE_POOLS = "multiple"  # Original strategy - multiple small pools
    SINGLE_SHARED = "shared"     # New strategy - single shared pool
    ADAPTIVE = "adaptive"        # Adaptive strategy based on workload


@dataclass
class ThreadMetrics:
    """Enhanced thread usage metrics."""
    active_threads: int
    daemon_threads: int
    total_threads: int
    max_threads_reached: int
    thread_pool_size: int
    memory_usage_mb: float
    # Phase 2 additions
    active_pools: int = 0
    cleanup_count: int = 0
    cpu_usage_percent: float = 0.0
    shared_pool_tasks: int = 0
    memory_throttled: bool = False


@dataclass
class TaskInfo:
    """Information about a submitted task."""
    task_id: str
    pool_name: str
    submitted_at: float
    future: Future
    priority: int = 0


class ThreadManager:
    """Enhanced thread manager with single shared pool and memory-based throttling."""

    def __init__(self,
                 max_threads: int = BackupConstants.DEFAULT_MAX_THREADS,
                 cleanup_interval: int = BackupConstants.DEFAULT_CLEANUP_INTERVAL_SECONDS,
                 strategy: PoolStrategy = PoolStrategy.SINGLE_SHARED):
        """
        Initialize thread manager.

        Args:
            max_threads: Maximum number of concurrent threads
            cleanup_interval: Cleanup interval in seconds
            strategy: Thread pool management strategy
        """
        self.max_threads = max_threads
        self.cleanup_interval = cleanup_interval
        self.strategy = strategy

        # Legacy multiple pools support
        self.active_pools: Dict[str, ThreadPoolExecutor] = {}
        self.pool_lock = threading.Lock()
        self.metrics_lock = threading.Lock()

        # Phase 2: Single shared pool
        self.shared_pool: Optional[ThreadPoolExecutor] = None
        self.shared_pool_lock = threading.Lock()
        self.active_tasks: Dict[str, TaskInfo] = {}
        self.task_counter = 0

        # Memory-based throttling
        self.memory_threshold_mb = (BackupConstants.MEMORY_WARNING_THRESHOLD / 100.0) * self._get_total_memory_mb()
        self.memory_critical_mb = (BackupConstants.MEMORY_CRITICAL_THRESHOLD / 100.0) * self._get_total_memory_mb()

        # Metrics tracking
        self.max_threads_reached = 0
        self.cleanup_count = 0
        self.memory_throttle_count = 0

        # Initialize shared pool if using single shared strategy
        if self.strategy == PoolStrategy.SINGLE_SHARED:
            self._initialize_shared_pool()

        # Start cleanup thread
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_worker,
            name="thread-manager-cleanup",
            daemon=True
        )
        self.cleanup_thread.start()

        logger.info(f"ThreadManager initialized: max_threads={max_threads}, strategy={strategy.value}")

    def _get_total_memory_mb(self) -> float:
        """Get total system memory in MB."""
        try:
            return psutil.virtual_memory().total / (1024 * 1024)
        except:
            # Fallback for your 30GB system
            return 30 * 1024  # 30GB in MB

    def _get_current_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / (1024 * 1024)
        except:
            return 0.0

    def _get_system_memory_usage_percent(self) -> float:
        """Get system memory usage percentage."""
        try:
            return psutil.virtual_memory().percent
        except:
            return 0.0

    def _initialize_shared_pool(self):
        """Initialize the single shared thread pool."""
        with self.shared_pool_lock:
            if self.shared_pool is None:
                # Use conservative pool size for shared pool
                pool_size = min(self.max_threads, BackupConstants.MAX_THREAD_POOL_SIZE)
                self.shared_pool = ThreadPoolExecutor(
                    max_workers=pool_size,
                    thread_name_prefix="shared-pool"
                )
                logger.info(f"Initialized shared thread pool with {pool_size} workers")

    def _is_memory_throttled(self) -> bool:
        """Check if memory usage requires throttling."""
        current_memory = self._get_current_memory_usage_mb()
        system_memory_percent = self._get_system_memory_usage_percent()

        # Throttle if either process memory or system memory is high
        process_throttle = current_memory > self.memory_threshold_mb
        system_throttle = system_memory_percent > BackupConstants.MEMORY_WARNING_THRESHOLD

        if process_throttle or system_throttle:
            logger.warning(f"Memory throttling active: process={current_memory:.1f}MB, "
                         f"system={system_memory_percent:.1f}%")
            self.memory_throttle_count += 1
            return True
        return False

    def get_metrics(self) -> ThreadMetrics:
        """Get current thread metrics."""
        with self.metrics_lock:
            active_count = threading.active_count()
            daemon_count = sum(1 for t in threading.enumerate() if t.daemon)
            
            # Get memory usage
            try:
                import psutil
                process = psutil.Process()
                memory_mb = process.memory_info().rss / (1024 * 1024)
            except ImportError:
                memory_mb = 0.0
            
            # Phase 2: Enhanced metrics
            shared_pool_tasks = len(self.active_tasks) if self.strategy == PoolStrategy.SINGLE_SHARED else 0
            cpu_percent = 0.0
            try:
                cpu_percent = psutil.cpu_percent(interval=None)
            except:
                pass

            return ThreadMetrics(
                active_threads=active_count,
                daemon_threads=daemon_count,
                total_threads=active_count,
                max_threads_reached=self.max_threads_reached,
                thread_pool_size=len(self.active_pools),
                memory_usage_mb=memory_mb,
                # Phase 2 additions
                active_pools=len(self.active_pools),
                cleanup_count=self.cleanup_count,
                cpu_usage_percent=cpu_percent,
                shared_pool_tasks=shared_pool_tasks,
                memory_throttled=self._is_memory_throttled()
            )

    def submit_task(self, func: Callable, *args, pool_name: str = "default", priority: int = 0, **kwargs) -> Future:
        """
        Submit a task to the shared thread pool (Phase 2 feature).

        Args:
            func: Function to execute
            *args: Function arguments
            pool_name: Logical pool name for tracking
            priority: Task priority (higher = more important)
            **kwargs: Function keyword arguments

        Returns:
            Future object for the submitted task
        """
        if self.strategy != PoolStrategy.SINGLE_SHARED:
            raise RuntimeError("submit_task only available with SINGLE_SHARED strategy")

        # Check memory throttling
        if self._is_memory_throttled():
            logger.warning(f"Memory throttling active, delaying task submission for {pool_name}")
            time.sleep(0.1)  # Brief delay to allow memory to free up

        # Ensure shared pool is initialized
        if self.shared_pool is None:
            self._initialize_shared_pool()

        # Generate unique task ID
        self.task_counter += 1
        task_id = f"{pool_name}-task-{self.task_counter}"

        # Submit task to shared pool
        with self.shared_pool_lock:
            future = self.shared_pool.submit(func, *args, **kwargs)

            # Track task info
            task_info = TaskInfo(
                task_id=task_id,
                pool_name=pool_name,
                submitted_at=time.time(),
                future=future,
                priority=priority
            )
            self.active_tasks[task_id] = task_info

            logger.debug(f"Submitted task {task_id} to shared pool")
            return future

    @contextmanager
    def get_thread_pool(self, pool_name: str, max_workers: Optional[int] = None):
        """
        Get or create a thread pool with automatic cleanup.
        Supports both multiple pools and single shared pool strategies.

        Args:
            pool_name: Unique name for the thread pool
            max_workers: Maximum workers (defaults to self.max_threads)
        """
        if max_workers is None:
            max_workers = min(self.max_threads, 4)  # Conservative default

        # Phase 2: If using single shared pool, return that instead
        if self.strategy == PoolStrategy.SINGLE_SHARED:
            if self.shared_pool is None:
                self._initialize_shared_pool()
            logger.debug(f"Using shared pool for {pool_name}")
            yield self.shared_pool
            return

        # Legacy multiple pools strategy
        pool = None
        try:
            with self.pool_lock:
                if pool_name in self.active_pools:
                    pool = self.active_pools[pool_name]
                else:
                    # Enhanced thread limit checking with Windows optimization
                    current_threads = threading.active_count()
                    projected_threads = current_threads + max_workers

                    # More conservative limits for Windows Server
                    if projected_threads > BackupConstants.THREAD_WARNING_THRESHOLD:
                        logger.warning(f"Thread limit approaching: {current_threads} active, "
                                     f"requested {max_workers} more, projected: {projected_threads}")
                        # Force cleanup before creating new pool
                        self._force_cleanup()

                        # Re-check after cleanup
                        current_threads = threading.active_count()
                        projected_threads = current_threads + max_workers

                        if projected_threads > BackupConstants.THREAD_CRITICAL_THRESHOLD:
                            logger.error(f"Cannot create pool: would exceed critical thread limit "
                                       f"({projected_threads} > {BackupConstants.THREAD_CRITICAL_THRESHOLD})")
                            raise RuntimeError(f"Thread limit exceeded: {projected_threads} threads would exceed limit")
                    
                    pool = ThreadPoolExecutor(
                        max_workers=max_workers,
                        thread_name_prefix=f"pool-{pool_name}"
                    )
                    self.active_pools[pool_name] = pool
                    
                    # Update metrics
                    if current_threads > self.max_threads_reached:
                        self.max_threads_reached = current_threads
            
            yield pool
            
        finally:
            # Don't shutdown immediately, let cleanup worker handle it
            pass
    
    def _cleanup_worker(self):
        """Background cleanup worker."""
        while True:
            try:
                time.sleep(self.cleanup_interval)
                self._perform_cleanup()
            except Exception as e:
                logger.error(f"Cleanup worker error: {e}")
    
    def _perform_cleanup(self):
        """Perform periodic cleanup for both legacy pools and shared pool tasks."""
        # Phase 2: Clean up completed shared pool tasks
        if self.strategy == PoolStrategy.SINGLE_SHARED:
            self._cleanup_shared_pool_tasks()

        # Legacy multiple pools cleanup
        with self.pool_lock:
            pools_to_remove = []

            for pool_name, pool in self.active_pools.items():
                # Check if pool is idle (no running tasks)
                if hasattr(pool, '_threads') and len(pool._threads) == 0:
                    pools_to_remove.append(pool_name)

            # Remove idle pools with proper synchronous shutdown
            for pool_name in pools_to_remove:
                try:
                    pool = self.active_pools.pop(pool_name)
                    # Use synchronous shutdown for proper cleanup
                    pool.shutdown(wait=True)
                    self.cleanup_count += 1
                    logger.debug(f"Cleaned up idle thread pool: {pool_name}")
                except Exception as e:
                    logger.error(f"Error cleaning up pool {pool_name}: {e}")
                    # Force shutdown if normal shutdown fails
                    try:
                        pool.shutdown(wait=False)
                    except:
                        pass
        
        # Force garbage collection
        gc.collect()

    def _cleanup_shared_pool_tasks(self):
        """Clean up completed tasks from shared pool tracking."""
        completed_tasks = []

        # Find completed tasks
        for task_id, task_info in self.active_tasks.items():
            if task_info.future.done():
                completed_tasks.append(task_id)

        # Remove completed tasks
        for task_id in completed_tasks:
            try:
                task_info = self.active_tasks.pop(task_id)
                # Log if task took a long time
                duration = time.time() - task_info.submitted_at
                if duration > 60:  # Log tasks that took more than 1 minute
                    logger.debug(f"Long-running task completed: {task_id} ({duration:.1f}s)")
            except KeyError:
                pass  # Task already removed

        if completed_tasks:
            logger.debug(f"Cleaned up {len(completed_tasks)} completed shared pool tasks")

    def _force_cleanup(self):
        """Force immediate cleanup of all pools with improved synchronous shutdown."""
        logger.info("Forcing immediate thread pool cleanup")

        with self.pool_lock:
            for pool_name, pool in list(self.active_pools.items()):
                try:
                    # Try synchronous shutdown first
                    pool.shutdown(wait=True)
                    del self.active_pools[pool_name]
                    logger.debug(f"Force cleaned pool: {pool_name}")
                except Exception as e:
                    logger.warning(f"Synchronous shutdown failed for {pool_name}: {e}")
                    try:
                        # Fallback to immediate shutdown
                        pool.shutdown(wait=False)
                        del self.active_pools[pool_name]
                        logger.debug(f"Force shutdown pool: {pool_name}")
                    except Exception as e2:
                        logger.error(f"Error force cleaning pool {pool_name}: {e2}")

        # Aggressive garbage collection with multiple rounds
        for i in range(3):
            collected = gc.collect()
            logger.debug(f"Garbage collection round {i+1}: {collected} objects collected")
            time.sleep(0.5)  # Give system time to clean up

        # Log current thread count after cleanup
        current_threads = threading.active_count()
        logger.info(f"Thread count after force cleanup: {current_threads}")

    def get_thread_metrics(self) -> Dict[str, int]:
        """Get current thread metrics for monitoring."""
        with self.pool_lock:
            active_pools = len(self.active_pools)
            pool_threads = sum(len(getattr(pool, '_threads', [])) for pool in self.active_pools.values())

        return {
            'total_threads': threading.active_count(),
            'active_pools': active_pools,
            'pool_threads': pool_threads,
            'max_threads_reached': self.max_threads_reached,
            'cleanup_count': self.cleanup_count,
            'thread_warning_threshold': BackupConstants.THREAD_WARNING_THRESHOLD,
            'thread_critical_threshold': BackupConstants.THREAD_CRITICAL_THRESHOLD
        }

    def emergency_shutdown(self):
        """Emergency shutdown of all thread pools - most aggressive cleanup."""
        logger.critical("EMERGENCY THREAD POOL SHUTDOWN INITIATED")

        try:
            with self.pool_lock:
                # Cancel all running tasks and shutdown immediately
                for pool_name, pool in list(self.active_pools.items()):
                    try:
                        # Cancel pending futures if possible
                        if hasattr(pool, '_threads'):
                            for thread in pool._threads:
                                if thread.is_alive():
                                    logger.warning(f"Terminating thread: {thread.name}")

                        # Try synchronous shutdown first, then fallback
                        try:
                            pool.shutdown(wait=True)
                        except:
                            pool.shutdown(wait=False)
                        del self.active_pools[pool_name]
                        logger.info(f"Emergency shutdown pool: {pool_name}")
                    except Exception as e:
                        logger.error(f"Error in emergency shutdown of pool {pool_name}: {e}")

                # Clear the pools dictionary
                self.active_pools.clear()

            # Multiple rounds of garbage collection
            for i in range(5):
                gc.collect()
                time.sleep(0.5)

            current_threads = threading.active_count()
            logger.critical(f"Emergency shutdown complete. Remaining threads: {current_threads}")

        except Exception as e:
            logger.error(f"Emergency shutdown failed: {e}")
    
    def shutdown(self):
        """Shutdown thread manager and all pools with improved cleanup."""
        logger.info("Shutting down ThreadManager")

        # Phase 2: Shutdown shared pool first
        if self.shared_pool is not None:
            with self.shared_pool_lock:
                try:
                    logger.info("Shutting down shared thread pool")
                    self.shared_pool.shutdown(wait=True)
                    logger.debug("Shared pool shutdown completed")
                except Exception as e:
                    logger.warning(f"Error shutting down shared pool: {e}")
                    try:
                        self.shared_pool.shutdown(wait=False)
                    except:
                        pass
                finally:
                    self.shared_pool = None
                    self.active_tasks.clear()

        # Legacy multiple pools shutdown
        with self.pool_lock:
            for pool_name, pool in list(self.active_pools.items()):
                try:
                    # Use synchronous shutdown
                    pool.shutdown(wait=True)
                    logger.debug(f"Shutdown pool: {pool_name}")
                except Exception as e:
                    logger.warning(f"Error shutting down pool {pool_name}: {e}")
                    try:
                        # Force shutdown if normal shutdown fails
                        pool.shutdown(wait=False)
                        logger.debug(f"Force shutdown pool: {pool_name}")
                    except Exception as e2:
                        logger.error(f"Error force shutting down pool {pool_name}: {e2}")

            self.active_pools.clear()

        # Enhanced final cleanup
        for i in range(3):
            collected = gc.collect()
            logger.debug(f"Final cleanup round {i+1}: {collected} objects collected")
            if i < 2:  # Don't sleep on last iteration
                time.sleep(0.3)


# Global thread manager instance
_thread_manager = None


def get_thread_manager() -> ThreadManager:
    """Get global thread manager instance with Phase 2 single shared pool strategy."""
    global _thread_manager
    if _thread_manager is None:
        # Phase 2: Use single shared pool strategy by default
        _thread_manager = ThreadManager(strategy=PoolStrategy.SINGLE_SHARED)
    return _thread_manager


@contextmanager
def managed_thread_pool(pool_name: str, max_workers: Optional[int] = None):
    """Context manager for managed thread pools."""
    manager = get_thread_manager()
    with manager.get_thread_pool(pool_name, max_workers) as pool:
        yield pool


def submit_shared_task(func: Callable, *args, pool_name: str = "default", priority: int = 0, **kwargs) -> Future:
    """
    Convenience function to submit a task to the shared thread pool.

    Args:
        func: Function to execute
        *args: Function arguments
        pool_name: Logical pool name for tracking
        priority: Task priority (higher = more important)
        **kwargs: Function keyword arguments

    Returns:
        Future object for the submitted task
    """
    manager = get_thread_manager()
    return manager.submit_task(func, *args, pool_name=pool_name, priority=priority, **kwargs)


def log_thread_metrics():
    """Log current thread metrics."""
    manager = get_thread_manager()
    metrics = manager.get_metrics()
    
    logger.info(f"Thread Metrics: Active={metrics.active_threads}, "
                f"Daemon={metrics.daemon_threads}, "
                f"Pools={metrics.thread_pool_size}, "
                f"Memory={metrics.memory_usage_mb:.1f}MB")
