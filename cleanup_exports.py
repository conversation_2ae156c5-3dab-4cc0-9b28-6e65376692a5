#!/usr/bin/env python3
"""
Cleanup Script for TNGD Backup System
=====================================

This script cleans up leftover JSON files in the data/exports directory
that were not properly cleaned up from interrupted or failed backup runs.

The backup system normally cleans up these files automatically, but if
a backup process is interrupted, these temporary files may remain.

Usage:
    python cleanup_exports.py [--dry-run] [--force]
    
Options:
    --dry-run    Show what would be deleted without actually deleting
    --force      Skip confirmation prompt
"""

import os
import sys
import argparse
from pathlib import Path
from typing import List, Tuple
import logging

def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)

def find_leftover_files(exports_dir: Path) -> List[Tuple[Path, int]]:
    """
    Find leftover JSON files in the exports directory.
    
    Returns:
        List of tuples (file_path, file_size_bytes)
    """
    leftover_files = []
    
    if not exports_dir.exists():
        return leftover_files
    
    # Look for JSON files in all subdirectories
    for json_file in exports_dir.rglob("*.json"):
        if json_file.is_file():
            try:
                file_size = json_file.stat().st_size
                leftover_files.append((json_file, file_size))
            except (OSError, PermissionError):
                # Skip files we can't access
                continue
    
    return leftover_files

def format_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def cleanup_files(files: List[Tuple[Path, int]], dry_run: bool = False) -> Tuple[int, int]:
    """
    Clean up the specified files.
    
    Returns:
        Tuple of (files_cleaned, total_size_freed)
    """
    files_cleaned = 0
    total_size_freed = 0
    
    for file_path, file_size in files:
        try:
            if dry_run:
                print(f"  [DRY-RUN] Would delete: {file_path.relative_to(Path('.'))} ({format_size(file_size)})")
            else:
                file_path.unlink()
                print(f"  [DELETED] {file_path.relative_to(Path('.'))} ({format_size(file_size)})")
            
            files_cleaned += 1
            total_size_freed += file_size
            
        except (OSError, PermissionError) as e:
            print(f"  [ERROR] Could not delete {file_path}: {e}")
    
    return files_cleaned, total_size_freed

def main():
    """Main cleanup function."""
    parser = argparse.ArgumentParser(
        description="Clean up leftover JSON files from interrupted backup runs",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='Show what would be deleted without actually deleting'
    )
    parser.add_argument(
        '--force', 
        action='store_true',
        help='Skip confirmation prompt'
    )
    
    args = parser.parse_args()
    logger = setup_logging()
    
    # Define exports directory
    exports_dir = Path("data/exports")
    
    logger.info("TNGD Backup System - Export Cleanup Tool")
    logger.info("=" * 50)
    
    # Find leftover files
    logger.info(f"Scanning {exports_dir} for leftover JSON files...")
    leftover_files = find_leftover_files(exports_dir)
    
    if not leftover_files:
        logger.info("✅ No leftover JSON files found - exports directory is clean!")
        return 0
    
    # Calculate totals
    total_files = len(leftover_files)
    total_size = sum(size for _, size in leftover_files)
    
    # Group by directory for better display
    files_by_dir = {}
    for file_path, file_size in leftover_files:
        dir_name = file_path.parent.name
        if dir_name not in files_by_dir:
            files_by_dir[dir_name] = []
        files_by_dir[dir_name].append((file_path, file_size))
    
    # Display findings
    logger.info(f"Found {total_files} leftover JSON files ({format_size(total_size)} total)")
    logger.info("")
    
    for dir_name, files in files_by_dir.items():
        dir_size = sum(size for _, size in files)
        logger.info(f"📁 {dir_name}: {len(files)} files ({format_size(dir_size)})")
        
        # Show first few files as examples
        for i, (file_path, file_size) in enumerate(files[:3]):
            logger.info(f"   • {file_path.name} ({format_size(file_size)})")
        
        if len(files) > 3:
            logger.info(f"   ... and {len(files) - 3} more files")
        logger.info("")
    
    # Confirmation
    if not args.force and not args.dry_run:
        response = input(f"Delete {total_files} files ({format_size(total_size)})? (y/N): ").strip().lower()
        if response != 'y':
            logger.info("Operation cancelled by user")
            return 0
    
    # Perform cleanup
    action = "DRY-RUN" if args.dry_run else "CLEANUP"
    logger.info(f"[{action}] Processing {total_files} files...")
    
    files_cleaned, size_freed = cleanup_files(leftover_files, dry_run=args.dry_run)
    
    # Summary
    logger.info("")
    if args.dry_run:
        logger.info(f"[DRY-RUN COMPLETE] Would delete {files_cleaned} files ({format_size(size_freed)})")
        logger.info("Run without --dry-run to actually delete the files")
    else:
        logger.info(f"[CLEANUP COMPLETE] Deleted {files_cleaned} files ({format_size(size_freed)} freed)")
        logger.info("✅ Export directory cleanup finished!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
