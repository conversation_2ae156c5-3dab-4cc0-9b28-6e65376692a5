#!/usr/bin/env python3
"""
Cleanup script to remove duplicate chunk files created by the chunking bug.
This script will remove all the duplicate chunk files and keep only the first unique chunk.
"""

import os
import hashlib
import json
from pathlib import Path

def get_file_hash(file_path):
    """Get MD5 hash of a file."""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def analyze_chunks(directory):
    """Analyze chunk files to identify duplicates."""
    chunk_files = []
    
    # Find all chunk files
    for file_path in Path(directory).glob("*.json"):
        if file_path.name.startswith("my_app_tngd_actiontraillinux"):
            chunk_files.append(file_path)
    
    if not chunk_files:
        print("No chunk files found.")
        return
    
    # Sort files by name to process in order
    chunk_files.sort(key=lambda x: x.name)
    
    print(f"Found {len(chunk_files)} chunk files")
    print("Analyzing file hashes...")
    
    # Calculate hashes and group by hash
    hash_groups = {}
    file_info = {}
    
    for file_path in chunk_files:
        file_hash = get_file_hash(file_path)
        if file_hash:
            file_size = file_path.stat().st_size
            file_info[file_path] = {
                'hash': file_hash,
                'size': file_size
            }
            
            if file_hash not in hash_groups:
                hash_groups[file_hash] = []
            hash_groups[file_hash].append(file_path)
    
    # Report findings
    print(f"\nAnalysis Results:")
    print(f"Total files: {len(chunk_files)}")
    print(f"Unique hashes: {len(hash_groups)}")
    
    duplicates_to_remove = []
    
    for file_hash, files in hash_groups.items():
        if len(files) > 1:
            print(f"\nDuplicate group (hash: {file_hash[:8]}...):")
            print(f"  Files with same content: {len(files)}")
            
            # Keep the first file, mark others for removal
            files_sorted = sorted(files, key=lambda x: x.name)
            keep_file = files_sorted[0]
            remove_files = files_sorted[1:]
            
            print(f"  Keeping: {keep_file.name}")
            for remove_file in remove_files:
                print(f"  Will remove: {remove_file.name}")
                duplicates_to_remove.append(remove_file)
    
    return duplicates_to_remove, file_info

def cleanup_duplicates(directory, dry_run=True):
    """Clean up duplicate chunk files."""
    print(f"\n{'DRY RUN - ' if dry_run else ''}Cleaning up duplicate chunks in: {directory}")
    print("=" * 60)
    
    duplicates_to_remove, file_info = analyze_chunks(directory)
    
    if not duplicates_to_remove:
        print("No duplicate files found to remove.")
        return
    
    print(f"\n{'Would remove' if dry_run else 'Removing'} {len(duplicates_to_remove)} duplicate files:")
    
    total_size_saved = 0
    
    for file_path in duplicates_to_remove:
        file_size = file_info[file_path]['size']
        total_size_saved += file_size
        
        print(f"  {'[DRY RUN] Would remove' if dry_run else 'Removing'}: {file_path.name} ({file_size / (1024*1024):.1f} MB)")
        
        if not dry_run:
            try:
                file_path.unlink()
                print(f"    ✅ Removed successfully")
            except Exception as e:
                print(f"    ❌ Error removing file: {e}")
    
    print(f"\nTotal space {'that would be' if dry_run else ''} saved: {total_size_saved / (1024*1024):.1f} MB")
    
    if dry_run:
        print("\nThis was a dry run. To actually remove the files, run with --execute")

def main():
    import sys
    
    # Check if we should actually execute the cleanup
    dry_run = "--execute" not in sys.argv
    
    # Directory containing the chunk files
    chunk_directory = "data/exports/20250415_000000"
    
    if not os.path.exists(chunk_directory):
        print(f"Directory not found: {chunk_directory}")
        print("Please check if the backup files are in a different location.")
        return
    
    print("TNGD Backup System - Duplicate Chunk Cleanup")
    print("=" * 60)
    
    if dry_run:
        print("Running in DRY RUN mode - no files will be deleted")
        print("Use --execute flag to actually remove duplicate files")
    else:
        print("EXECUTING cleanup - duplicate files will be permanently deleted")
        response = input("Are you sure you want to continue? (yes/no): ")
        if response.lower() != 'yes':
            print("Cleanup cancelled.")
            return
    
    cleanup_duplicates(chunk_directory, dry_run=dry_run)
    
    if dry_run:
        print(f"\nTo execute the cleanup, run: python {sys.argv[0]} --execute")

if __name__ == "__main__":
    main()
